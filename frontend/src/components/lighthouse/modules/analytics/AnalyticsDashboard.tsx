import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  FileText,
  MessageSquare,
  Bot,
  Search,
  Brain,
  Clock,
  Target,
  Zap,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  Pie<PERSON>hart,
  LineChart,
  MoreHorizontal,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { format, subDays, startOfWeek, endOfWeek } from 'date-fns';

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
  color: string;
}

interface ActivityData {
  date: string;
  knowledge: number;
  research: number;
  agents: number;
  chat: number;
}

export function AnalyticsDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('7d');
  
  const {
    currentProject,
    knowledgeCollections,
    activeAgents,
    agentHistory,
    projectIntelligence,
  } = useLighthouseStore();

  // Mock analytics data - would come from actual usage tracking
  const metrics: MetricCard[] = [
    {
      title: 'Knowledge Usage',
      value: '89%',
      change: 12,
      trend: 'up',
      icon: <FileText className="h-5 w-5" />,
      color: 'text-blue-500'
    },
    {
      title: 'Research Sessions',
      value: 23,
      change: 8,
      trend: 'up', 
      icon: <Search className="h-5 w-5" />,
      color: 'text-green-500'
    },
    {
      title: 'Agent Tasks',
      value: 47,
      change: -3,
      trend: 'down',
      icon: <Bot className="h-5 w-5" />,
      color: 'text-purple-500'
    },
    {
      title: 'Chat Interactions',
      value: 156,
      change: 23,
      trend: 'up',
      icon: <MessageSquare className="h-5 w-5" />,
      color: 'text-orange-500'
    },
    {
      title: 'Learning Progress',
      value: `${projectIntelligence?.learningLevel || 67}%`,
      change: 15,
      trend: 'up',
      icon: <Brain className="h-5 w-5" />,
      color: 'text-indigo-500'
    },
    {
      title: 'Productivity Score',
      value: 8.4,
      change: 0.7,
      trend: 'up',
      icon: <Target className="h-5 w-5" />,
      color: 'text-emerald-500'
    }
  ];

  // Mock activity data for charts
  const activityData: ActivityData[] = [
    { date: '2024-01-15', knowledge: 12, research: 8, agents: 5, chat: 23 },
    { date: '2024-01-16', knowledge: 19, research: 12, agents: 8, chat: 31 },
    { date: '2024-01-17', knowledge: 15, research: 15, agents: 12, chat: 28 },
    { date: '2024-01-18', knowledge: 22, research: 9, agents: 6, chat: 35 },
    { date: '2024-01-19', knowledge: 18, research: 18, agents: 15, chat: 42 },
    { date: '2024-01-20', knowledge: 25, research: 14, agents: 9, chat: 38 },
    { date: '2024-01-21', knowledge: 21, research: 11, agents: 11, chat: 33 }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <ArrowUp className="h-3 w-3" />;
      case 'down':
        return <ArrowDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-500';
      case 'down':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  if (!currentProject) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Project Selected</h3>
          <p className="text-muted-foreground">
            Select or create a project to view analytics.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Sidebar - Quick Stats */}
      <aside className="w-80 border-r bg-muted/50 p-6 space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics Hub
          </h3>
          
          {/* Time Range Selector */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Time Range</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-2">
                {['7d', '30d', '90d'].map((range) => (
                  <Button
                    key={range}
                    size="sm"
                    variant={timeRange === range ? 'default' : 'outline'}
                    onClick={() => setTimeRange(range)}
                    className="text-xs"
                  >
                    {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Metrics */}
          <div className="space-y-3">
            {metrics.slice(0, 4).map((metric, index) => (
              <Card key={index} className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={cn("p-1 rounded", metric.color)}>
                      {metric.icon}
                    </div>
                    <div>
                      <div className="font-bold">{metric.value}</div>
                      <p className="text-xs text-muted-foreground">{metric.title}</p>
                    </div>
                  </div>
                  <div className={cn("flex items-center gap-1 text-xs", getTrendColor(metric.trend))}>
                    {getTrendIcon(metric.trend)}
                    {Math.abs(metric.change)}%
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Module Usage */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Module Usage</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Knowledge Hub</span>
                  <span>89%</span>
                </div>
                <Progress value={89} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Research Studio</span>
                  <span>76%</span>
                </div>
                <Progress value={76} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Agent Workspace</span>
                  <span>63%</span>
                </div>
                <Progress value={63} className="h-2" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Contextual Chat</span>
                  <span>92%</span>
                </div>
                <Progress value={92} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button size="sm" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Data
              </Button>
              <Button size="sm" variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Report
              </Button>
            </CardContent>
          </Card>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
              <p className="text-muted-foreground">
                Usage insights and performance metrics for {currentProject.name}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {metrics.map((metric, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className={cn("p-2 rounded-lg", metric.color.replace('text-', 'bg-').replace('-500', '-100'))}>
                      {metric.icon}
                    </div>
                    <div className={cn("flex items-center gap-1 text-sm", getTrendColor(metric.trend))}>
                      {getTrendIcon(metric.trend)}
                      {Math.abs(metric.change)}%
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="text-2xl font-bold">{metric.value}</div>
                    <p className="text-muted-foreground text-sm">{metric.title}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Analytics Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="usage">Usage Patterns</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="intelligence">Intelligence</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Activity Chart */}
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <LineChart className="h-5 w-5" />
                      Activity Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <LineChart className="h-8 w-8 mx-auto mb-2" />
                        <p>Interactive activity chart would be displayed here</p>
                        <p className="text-sm">Showing usage across all modules over time</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Top Sources */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Most Used Sources</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {knowledgeCollections.slice(0, 5).map((collection, index) => (
                        <div key={collection.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 rounded bg-primary/10 flex items-center justify-center text-xs font-medium">
                              {index + 1}
                            </div>
                            <span className="text-sm">{collection.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary rounded-full"
                                style={{ width: `${85 - index * 15}%` }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {85 - index * 15}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Agent Performance */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Agent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {activeAgents.slice(0, 3).map((agent, index) => (
                        <div key={agent.id} className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>{agent.name}</span>
                            <Badge variant="outline">
                              {agent.status}
                            </Badge>
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span>Efficiency</span>
                              <span>{95 - index * 8}%</span>
                            </div>
                            <Progress value={95 - index * 8} className="h-1" />
                          </div>
                        </div>
                      ))}
                      {activeAgents.length === 0 && (
                        <p className="text-sm text-muted-foreground">No active agents</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="usage" className="mt-6">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Patterns</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <PieChart className="h-8 w-8 mx-auto mb-2" />
                        <p>Usage pattern analysis would be displayed here</p>
                        <p className="text-sm">Breakdown by modules, time of day, and activity type</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Peak Usage Hours</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {['09:00 - 11:00', '14:00 - 16:00', '19:00 - 21:00'].map((time, index) => (
                          <div key={time} className="flex items-center justify-between text-sm">
                            <span>{time}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-12 h-2 bg-muted rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-primary rounded-full"
                                  style={{ width: `${90 - index * 20}%` }}
                                />
                              </div>
                              <span className="text-xs">{90 - index * 20}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Feature Adoption</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          { feature: 'Smart Collections', adoption: 95 },
                          { feature: 'Agent Templates', adoption: 78 },
                          { feature: 'Source Grounding', adoption: 88 },
                          { feature: 'Context Learning', adoption: 72 }
                        ].map((item) => (
                          <div key={item.feature} className="space-y-1">
                            <div className="flex items-center justify-between text-sm">
                              <span>{item.feature}</span>
                              <span>{item.adoption}%</span>
                            </div>
                            <Progress value={item.adoption} className="h-1" />
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="mt-6">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Response Times
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold mb-2">1.2s</div>
                      <p className="text-sm text-muted-foreground">Average response time</p>
                      <div className="mt-4 space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Knowledge queries</span>
                          <span>0.8s</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Research searches</span>
                          <span>1.4s</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Agent tasks</span>
                          <span>2.1s</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Accuracy Metrics
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold mb-2">94.2%</div>
                      <p className="text-sm text-muted-foreground">Overall accuracy</p>
                      <div className="mt-4 space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Source relevance</span>
                          <span>96%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Answer quality</span>
                          <span>93%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Context awareness</span>
                          <span>89%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Activity className="h-4 w-4" />
                        System Health
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold mb-2 text-green-500">Excellent</div>
                      <p className="text-sm text-muted-foreground">System status</p>
                      <div className="mt-4 space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Uptime</span>
                          <span className="text-green-500">99.9%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Memory usage</span>
                          <span>67%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Processing queue</span>
                          <span>2 items</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="intelligence" className="mt-6">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5" />
                      Intelligence Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Learning Progress</span>
                            <span className="text-sm">{projectIntelligence?.learningLevel || 67}%</span>
                          </div>
                          <Progress value={projectIntelligence?.learningLevel || 67} className="h-2" />
                        </div>
                        
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Context Awareness</span>
                            <span className="text-sm">89%</span>
                          </div>
                          <Progress value={89} className="h-2" />
                        </div>
                        
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">Knowledge Integration</span>
                            <span className="text-sm">76%</span>
                          </div>
                          <Progress value={76} className="h-2" />
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-3">Recent Insights</h4>
                        <div className="space-y-2">
                          {projectIntelligence?.keyInsights?.slice(0, 3).map((insight, index) => (
                            <div key={index} className="text-sm p-2 rounded bg-muted">
                              {insight.title}
                            </div>
                          )) || (
                            <p className="text-sm text-muted-foreground">
                              No insights generated yet
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}