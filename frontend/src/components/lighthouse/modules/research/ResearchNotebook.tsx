import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Textarea } from '~/components/ui/textarea';
import { Input } from '~/components/ui/input';
import {
  BookOpen,
  Plus,
  Edit,
  Save,
  Quote,
  Link2,
  Lightbulb,
  FileText,
} from 'lucide-react';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

export function ResearchNotebook() {
  const [notes, setNotes] = useState('');
  const [hypotheses, setHypotheses] = useState('');
  const [conclusions, setConclusions] = useState('');
  
  const { currentProject, insights } = useLighthouseStore();

  return (
    <div className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Research Notes */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Edit className="h-4 w-4" />
              Research Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Record your research observations, key findings, and insights here..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[200px] resize-none"
            />
            <div className="flex justify-between items-center mt-3">
              <span className="text-xs text-muted-foreground">
                {notes.length} characters
              </span>
              <Button size="sm">
                <Save className="h-3 w-3 mr-1" />
                Save Notes
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Hypotheses */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Hypotheses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Develop and test your research hypotheses..."
              value={hypotheses}
              onChange={(e) => setHypotheses(e.target.value)}
              className="min-h-[200px] resize-none"
            />
            <div className="flex justify-between items-center mt-3">
              <span className="text-xs text-muted-foreground">
                Research hypotheses and predictions
              </span>
              <Button size="sm">
                <Save className="h-3 w-3 mr-1" />
                Save
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Key Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Quote className="h-5 w-5" />
            Connected Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {insights.slice(0, 5).map((insight) => (
              <div key={insight.id} className="p-3 rounded-lg border bg-muted/50">
                <p className="text-sm mb-2">{insight.content}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      {Math.round(insight.confidence * 100)}% confidence
                    </Badge>
                    {insight.connections.length > 0 && (
                      <Badge variant="outline">
                        <Link2 className="h-3 w-3 mr-1" />
                        {insight.connections.length} connections
                      </Badge>
                    )}
                  </div>
                  <Button size="sm" variant="ghost">
                    Add to Notebook
                  </Button>
                </div>
              </div>
            ))}
            {insights.length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-8">
                No insights generated yet. Start researching to see connected insights here.
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conclusions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Research Conclusions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Summarize your research conclusions and next steps..."
            value={conclusions}
            onChange={(e) => setConclusions(e.target.value)}
            className="min-h-[150px] resize-none"
          />
          <div className="flex justify-between items-center mt-3">
            <span className="text-xs text-muted-foreground">
              Document your final conclusions and future research directions
            </span>
            <div className="flex gap-2">
              <Button size="sm" variant="outline">
                Export Notebook
              </Button>
              <Button size="sm">
                <Save className="h-3 w-3 mr-1" />
                Save All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}