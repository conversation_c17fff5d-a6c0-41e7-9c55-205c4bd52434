import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Badge } from '~/components/ui/badge';
import { Slider } from '~/components/ui/slider';
import {
  Bot,
  Brain,
  Monitor,
  Zap,
  Search,
  FileText,
  Settings,
  Play,
  Plus,
} from 'lucide-react';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

export function AgentDeployment() {
  const [agentName, setAgentName] = useState('');
  const [agentGoal, setAgentGoal] = useState('');
  const [agentType, setAgentType] = useState<'research' | 'analysis' | 'execution' | 'monitoring'>('research');
  const [autonomyLevel, setAutonomyLevel] = useState([7]);
  const [selectedCapabilities, setSelectedCapabilities] = useState<string[]>(['web-search', 'analysis']);
  
  const { currentProject, deployAgent } = useLighthouseStore();

  const agentTypes = [
    { id: 'research', label: 'Research Agent', icon: Search, description: 'Autonomously research topics and gather information' },
    { id: 'analysis', label: 'Analysis Agent', icon: Brain, description: 'Analyze data and generate insights' },
    { id: 'execution', label: 'Execution Agent', icon: Zap, description: 'Execute tasks and workflows automatically' },
    { id: 'monitoring', label: 'Monitoring Agent', icon: Monitor, description: 'Monitor systems and report changes' },
  ];

  const capabilities = [
    'web-search', 'data-analysis', 'document-processing', 'api-access',
    'file-management', 'email-integration', 'scheduling', 'reporting'
  ];

  const handleDeploy = async () => {
    if (!currentProject || !agentName || !agentGoal) return;

    await deployAgent({
      projectId: currentProject.id,
      name: agentName,
      type: agentType,
      context: {
        goal: agentGoal,
        constraints: ['Respect data privacy', 'Use verified sources only'],
        knowledgeAccess: [], // Would be populated with relevant knowledge IDs
        capabilities: selectedCapabilities,
        autonomyLevel: autonomyLevel[0],
      },
    });

    // Reset form
    setAgentName('');
    setAgentGoal('');
    setSelectedCapabilities(['web-search', 'analysis']);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Deploy New Agent
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Agent Type Selection */}
          <div>
            <label className="text-sm font-medium mb-3 block">Agent Type</label>
            <div className="grid grid-cols-2 gap-3">
              {agentTypes.map(({ id, label, icon: Icon, description }) => (
                <Button
                  key={id}
                  variant={agentType === id ? 'default' : 'outline'}
                  className="h-auto p-4 flex flex-col items-start gap-2"
                  onClick={() => setAgentType(id as any)}
                >
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="font-medium">{label}</span>
                  </div>
                  <p className="text-xs text-left opacity-70">{description}</p>
                </Button>
              ))}
            </div>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* Agent Name */}
            <div>
              <label className="text-sm font-medium mb-2 block">Agent Name</label>
              <Input
                placeholder="e.g., Research Assistant Alpha"
                value={agentName}
                onChange={(e) => setAgentName(e.target.value)}
              />
            </div>

            {/* Autonomy Level */}
            <div>
              <label className="text-sm font-medium mb-2 block">
                Autonomy Level: {autonomyLevel[0]}/10
              </label>
              <Slider
                value={autonomyLevel}
                onValueChange={setAutonomyLevel}
                max={10}
                min={1}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Higher autonomy = more independent decision making
              </p>
            </div>
          </div>

          {/* Goal */}
          <div>
            <label className="text-sm font-medium mb-2 block">Primary Goal</label>
            <Textarea
              placeholder="Describe what this agent should accomplish..."
              value={agentGoal}
              onChange={(e) => setAgentGoal(e.target.value)}
              rows={3}
            />
          </div>

          {/* Capabilities */}
          <div>
            <label className="text-sm font-medium mb-3 block">Capabilities</label>
            <div className="flex flex-wrap gap-2">
              {capabilities.map((capability) => (
                <Badge
                  key={capability}
                  variant={selectedCapabilities.includes(capability) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedCapabilities(prev => 
                      prev.includes(capability)
                        ? prev.filter(c => c !== capability)
                        : [...prev, capability]
                    );
                  }}
                >
                  {capability}
                </Badge>
              ))}
            </div>
          </div>

          {/* Deploy Button */}
          <Button
            onClick={handleDeploy}
            disabled={!agentName || !agentGoal}
            className="w-full"
            size="lg"
          >
            <Play className="h-4 w-4 mr-2" />
            Deploy Agent
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}