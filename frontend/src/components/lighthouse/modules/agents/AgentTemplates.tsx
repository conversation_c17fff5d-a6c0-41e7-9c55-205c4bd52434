import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  Bot,
  Template,
  Download,
  Star,
  Zap,
} from 'lucide-react';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

export function AgentTemplates() {
  const { deployAgent, currentProject } = useLighthouseStore();

  const templates = [
    {
      id: 'research-assistant',
      name: 'Research Assistant',
      description: 'Autonomous research agent that can gather information from multiple sources',
      type: 'research',
      autonomy: 8,
      capabilities: ['web-search', 'document-processing', 'analysis'],
      popular: true,
    },
    {
      id: 'data-analyst',
      name: 'Data Analyst',
      description: 'Analyzes datasets and generates insights with visualizations',
      type: 'analysis',
      autonomy: 7,
      capabilities: ['data-analysis', 'reporting', 'visualization'],
      popular: true,
    },
    {
      id: 'content-monitor',
      name: 'Content Monitor',
      description: 'Monitors websites and APIs for changes and updates',
      type: 'monitoring',
      autonomy: 9,
      capabilities: ['web-monitoring', 'api-access', 'notifications'],
      popular: false,
    },
    {
      id: 'workflow-executor',
      name: 'Workflow Executor',
      description: 'Executes predefined workflows and handles task automation',
      type: 'execution',
      autonomy: 6,
      capabilities: ['workflow-execution', 'file-management', 'scheduling'],
      popular: false,
    },
  ];

  const handleUseTemplate = async (template: any) => {
    if (!currentProject) return;

    await deployAgent({
      projectId: currentProject.id,
      name: template.name,
      type: template.type,
      context: {
        goal: template.description,
        constraints: ['Respect data privacy', 'Use verified sources only'],
        knowledgeAccess: [],
        capabilities: template.capabilities,
        autonomyLevel: template.autonomy,
      },
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2">
        {templates.map((template) => (
          <Card key={template.id} className="relative">
            {template.popular && (
              <div className="absolute top-3 right-3">
                <Badge variant="default" className="gap-1">
                  <Star className="h-3 w-3" />
                  Popular
                </Badge>
              </div>
            )}
            
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Bot className="h-4 w-4" />
                {template.name}
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {template.description}
                </p>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Type:</span>
                  <Badge variant="outline" className="capitalize">
                    {template.type}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Autonomy:</span>
                  <div className="flex items-center gap-2">
                    {[...Array(10)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-1.5 h-1.5 rounded-full ${
                          i < template.autonomy ? 'bg-primary' : 'bg-muted'
                        }`}
                      />
                    ))}
                    <span className="text-xs">{template.autonomy}/10</span>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Capabilities:</p>
                  <div className="flex flex-wrap gap-1">
                    {template.capabilities.map((capability) => (
                      <Badge key={capability} variant="secondary" className="text-xs">
                        {capability}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleUseTemplate(template)}
                  >
                    <Zap className="h-3 w-3 mr-1" />
                    Deploy
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardContent className="text-center py-8">
          <Template className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
          <h3 className="font-medium mb-2">Custom Templates</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create and save your own agent templates for repeated use
          </p>
          <Button variant="outline">
            Create Template
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}