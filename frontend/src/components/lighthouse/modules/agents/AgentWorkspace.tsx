import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Bot,
  Plus,
  Play,
  Pause,
  Square,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  Monitor,
  Zap,
  Target,
  Brain,
  Activity,
} from 'lucide-react';\nimport { cn } from '~/lib/utils';\nimport { useLighthouseStore } from '../../shared/store/lighthouse-store';\nimport { AgentDeployment } from './AgentDeployment';\nimport { ProcessMonitor } from './ProcessMonitor';\nimport { AgentTemplates } from './AgentTemplates';\n\nexport function AgentWorkspace() {\n  const [activeTab, setActiveTab] = useState('active');\n  const {\n    currentProject,\n    activeAgents,\n    agentHistory,\n    deployAgent,\n    updateAgentStatus,\n  } = useLighthouseStore();\n\n  if (!currentProject) return null;\n\n  // Agent metrics\n  const runningAgents = activeAgents.filter(a => a.status === 'running').length;\n  const completedToday = agentHistory.filter(a => \n    a.status === 'completed' && \n    new Date().toDateString() === new Date().toDateString() // Today's completed agents\n  ).length;\n  const successRate = agentHistory.length > 0 \n    ? (agentHistory.filter(a => a.status === 'completed').length / agentHistory.length) * 100\n    : 0;\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'running':\n        return <Play className=\"h-4 w-4 text-blue-500 animate-pulse\" />;\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'failed':\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'bg-blue-50 border-blue-200 text-blue-700';\n      case 'completed':\n        return 'bg-green-50 border-green-200 text-green-700';\n      case 'failed':\n        return 'bg-red-50 border-red-200 text-red-700';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-700';\n    }\n  };\n\n  const handleStopAgent = (agentId: string) => {\n    updateAgentStatus(agentId, 'completed');\n  };\n\n  const handleRestartAgent = async (agent: any) => {\n    const newAgent = {\n      ...agent,\n      name: `${agent.name} (Restarted)`,\n    };\n    delete newAgent.id;\n    await deployAgent(newAgent);\n  };\n\n  return (\n    <div className=\"flex h-full\">\n      {/* Left Sidebar - Agent Overview */}\n      <aside className=\"w-80 border-r bg-muted/50 p-6 space-y-6\">\n        {/* Agent Stats */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold flex items-center gap-2\">\n            <Bot className=\"h-5 w-5\" />\n            Agent Workspace\n          </h3>\n          \n          <div className=\"grid grid-cols-1 gap-3\">\n            <Card className=\"p-3\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{runningAgents}</div>\n                  <p className=\"text-xs text-muted-foreground\">Running Now</p>\n                </div>\n                <Activity className=\"h-6 w-6 text-blue-500\" />\n              </div>\n            </Card>\n            \n            <Card className=\"p-3\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{completedToday}</div>\n                  <p className=\"text-xs text-muted-foreground\">Completed Today</p>\n                </div>\n                <CheckCircle className=\"h-6 w-6 text-green-500\" />\n              </div>\n            </Card>\n            \n            <Card className=\"p-3\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{Math.round(successRate)}%</div>\n                  <p className=\"text-xs text-muted-foreground\">Success Rate</p>\n                </div>\n                <Target className=\"h-6 w-6 text-purple-500\" />\n              </div>\n            </Card>\n            \n            <Card className=\"p-3\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-2xl font-bold\">{agentHistory.length + activeAgents.length}</div>\n                  <p className=\"text-xs text-muted-foreground\">Total Deployed</p>\n                </div>\n                <Bot className=\"h-6 w-6 text-gray-500\" />\n              </div>\n            </Card>\n          </div>\n        </div>\n\n        {/* Quick Deploy */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-sm\">Quick Deploy</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-2\">\n            <Button size=\"sm\" className=\"w-full justify-start\">\n              <Brain className=\"h-4 w-4 mr-2\" />\n              Research Agent\n            </Button>\n            <Button size=\"sm\" variant=\"outline\" className=\"w-full justify-start\">\n              <Monitor className=\"h-4 w-4 mr-2\" />\n              Monitoring Agent\n            </Button>\n            <Button size=\"sm\" variant=\"outline\" className=\"w-full justify-start\">\n              <Zap className=\"h-4 w-4 mr-2\" />\n              Analysis Agent\n            </Button>\n            <Button size=\"sm\" variant=\"outline\" className=\"w-full justify-start\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Custom Agent\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* System Status */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-sm flex items-center gap-2\">\n              <Activity className=\"h-4 w-4\" />\n              System Status\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">CPU Usage</span>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-16 h-1.5 bg-muted rounded-full overflow-hidden\">\n                    <div className=\"h-full bg-blue-500 rounded-full\" style={{ width: '45%' }} />\n                  </div>\n                  <span className=\"text-xs\">45%</span>\n                </div>\n              </div>\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">Memory</span>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-16 h-1.5 bg-muted rounded-full overflow-hidden\">\n                    <div className=\"h-full bg-green-500 rounded-full\" style={{ width: '67%' }} />\n                  </div>\n                  <span className=\"text-xs\">67%</span>\n                </div>\n              </div>\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-muted-foreground\">Queue</span>\n                <Badge variant=\"outline\">{activeAgents.filter(a => a.status === 'idle').length} pending</Badge>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </aside>\n\n      {/* Main Content */}\n      <main className=\"flex-1 p-6\">\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"mb-6\">\n            <TabsTrigger value=\"active\" className=\"gap-2\">\n              <Activity className=\"h-4 w-4\" />\n              Active Agents ({runningAgents})\n            </TabsTrigger>\n            <TabsTrigger value=\"deploy\" className=\"gap-2\">\n              <Plus className=\"h-4 w-4\" />\n              Deploy New\n            </TabsTrigger>\n            <TabsTrigger value=\"monitor\" className=\"gap-2\">\n              <Monitor className=\"h-4 w-4\" />\n              Process Monitor\n            </TabsTrigger>\n            <TabsTrigger value=\"templates\" className=\"gap-2\">\n              <Bot className=\"h-4 w-4\" />\n              Templates\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"active\" className=\"mt-0\">\n            <div className=\"space-y-4\">\n              {/* Running Agents */}\n              {activeAgents.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {activeAgents.map((agent) => (\n                    <Card key={agent.id} className={cn(\"border-l-4\", getStatusColor(agent.status))}>\n                      <CardHeader className=\"pb-3\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex items-start gap-3\">\n                            {getStatusIcon(agent.status)}\n                            <div>\n                              <CardTitle className=\"text-base\">{agent.name}</CardTitle>\n                              <p className=\"text-sm text-muted-foreground mt-1\">\n                                {agent.context.goal}\n                              </p>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center gap-2\">\n                            <Badge variant=\"outline\" className=\"capitalize\">\n                              {agent.type}\n                            </Badge>\n                            <Badge variant={agent.status === 'running' ? 'default' : 'secondary'}>\n                              {agent.status}\n                            </Badge>\n                          </div>\n                        </div>\n                      </CardHeader>\n                      \n                      <CardContent>\n                        <div className=\"space-y-4\">\n                          {/* Progress */}\n                          {agent.status === 'running' && (\n                            <div>\n                              <div className=\"flex items-center justify-between text-sm mb-1\">\n                                <span className=\"text-muted-foreground\">Progress</span>\n                                <span>Processing...</span>\n                              </div>\n                              <Progress value={65} className=\"h-2\" />\n                            </div>\n                          )}\n\n                          {/* Autonomy Level */}\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <span className=\"text-muted-foreground\">Autonomy Level</span>\n                            <div className=\"flex items-center gap-2\">\n                              {[...Array(10)].map((_, i) => (\n                                <div\n                                  key={i}\n                                  className={cn(\n                                    \"w-2 h-2 rounded-full\",\n                                    i < agent.context.autonomyLevel ? \"bg-primary\" : \"bg-muted\"\n                                  )}\n                                />\n                              ))}\n                              <span className=\"text-xs ml-1\">{agent.context.autonomyLevel}/10</span>\n                            </div>\n                          </div>\n\n                          {/* Capabilities */}\n                          <div>\n                            <p className=\"text-sm text-muted-foreground mb-2\">Capabilities</p>\n                            <div className=\"flex flex-wrap gap-1\">\n                              {agent.context.capabilities.map((capability, index) => (\n                                <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                                  {capability}\n                                </Badge>\n                              ))}\n                            </div>\n                          </div>\n\n                          {/* Results Preview */}\n                          {agent.results && agent.results.length > 0 && (\n                            <div>\n                              <p className=\"text-sm text-muted-foreground mb-2\">\n                                Recent Results ({agent.results.length})\n                              </p>\n                              <div className=\"space-y-1\">\n                                {agent.results.slice(0, 2).map((result) => (\n                                  <div key={result.id} className=\"text-xs p-2 rounded bg-muted\">\n                                    <span className=\"font-medium\">{result.type}:</span> {result.content}\n                                  </div>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n\n                          {/* Actions */}\n                          <div className=\"flex items-center gap-2 pt-2\">\n                            {agent.status === 'running' && (\n                              <Button\n                                size=\"sm\"\n                                variant=\"outline\"\n                                onClick={() => handleStopAgent(agent.id)}\n                              >\n                                <Square className=\"h-3 w-3 mr-1\" />\n                                Stop\n                              </Button>\n                            )}\n                            <Button size=\"sm\" variant=\"ghost\">\n                              <Monitor className=\"h-3 w-3 mr-1\" />\n                              Monitor\n                            </Button>\n                            <Button size=\"sm\" variant=\"ghost\">\n                              <Settings className=\"h-3 w-3 mr-1\" />\n                              Configure\n                            </Button>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              ) : (\n                <Card>\n                  <CardContent className=\"text-center py-12\">\n                    <Bot className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium mb-2\">No Active Agents</h3>\n                    <p className=\"text-muted-foreground mb-4 max-w-md mx-auto\">\n                      Deploy your first AI agent to start autonomous task execution.\n                      Agents can work independently while you focus on strategic decisions.\n                    </p>\n                    <Button onClick={() => setActiveTab('deploy')}>\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      Deploy First Agent\n                    </Button>\n                  </CardContent>\n                </Card>\n              )}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"deploy\" className=\"mt-0\">\n            <AgentDeployment />\n          </TabsContent>\n\n          <TabsContent value=\"monitor\" className=\"mt-0\">\n            <ProcessMonitor />\n          </TabsContent>\n\n          <TabsContent value=\"templates\" className=\"mt-0\">\n            <AgentTemplates />\n          </TabsContent>\n        </Tabs>\n      </main>\n    </div>\n  );\n}"