// Main exports for Lighthouse components
export { LighthouseMain, default } from './core/LighthouseMain';
export { ProjectContext } from './core/ProjectContext';
export { NavigationSystem } from './core/NavigationSystem';
export { ModuleContainer } from './core/ModuleContainer';

// Type exports
export * from './types/navigation.types';
export * from './types/project.types';
export * from './types/knowledge.types';
export * from './types/intelligence.types';

// Store exports
export { useLighthouseStore } from './shared/store/lighthouse-store';

// Module exports
export { Dashboard } from './modules/dashboard/Dashboard';
export { KnowledgeHub } from './modules/knowledge/KnowledgeHub';
export { ResearchStudio } from './modules/research/ResearchStudio';
export { AgentWorkspace } from './modules/agents/AgentWorkspace';
export { ContextualChat } from './modules/chat/ContextualChat';
export { SourceManager } from './modules/sources/SourceManager';
export { AnalyticsDashboard } from './modules/analytics/AnalyticsDashboard';