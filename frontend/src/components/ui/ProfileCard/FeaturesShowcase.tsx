import React from 'react';
import DemoNavigation from './DemoNavigation';

interface FeatureCardProps {
  icon: string;
  title: string;
  description: string;
  features: string[];
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, features }) => (
  <div style={{
    background: 'rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    borderRadius: '12px',
    padding: '1.5rem',
    transition: 'all 0.3s ease',
    cursor: 'pointer'
  }}
  onMouseEnter={(e) => {
    e.currentTarget.style.transform = 'translateY(-5px)';
    e.currentTarget.style.borderColor = 'rgba(168, 85, 247, 0.3)';
    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
  }}
  onMouseLeave={(e) => {
    e.currentTarget.style.transform = 'translateY(0)';
    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
  }}
  >
    <div style={{
      fontSize: '2rem',
      marginBottom: '1rem',
      textAlign: 'center'
    }}>
      {icon}
    </div>
    <h3 style={{
      color: '#a78bfa',
      margin: '0 0 0.5rem 0',
      fontSize: '1.2rem',
      fontWeight: '600'
    }}>
      {title}
    </h3>
    <p style={{
      color: 'rgba(255, 255, 255, 0.8)',
      margin: '0 0 1rem 0',
      fontSize: '0.9rem',
      lineHeight: '1.5'
    }}>
      {description}
    </p>
    <ul style={{
      listStyle: 'none',
      padding: 0,
      margin: 0
    }}>
      {features.map((feature, index) => (
        <li key={index} style={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: '0.8rem',
          marginBottom: '0.25rem',
          paddingLeft: '1rem',
          position: 'relative'
        }}>
          <span style={{
            position: 'absolute',
            left: 0,
            color: '#06b6d4'
          }}>
            ✓
          </span>
          {feature}
        </li>
      ))}
    </ul>
  </div>
);

const FeaturesShowcase: React.FC = () => {
  const features = [
    {
      icon: '🎨',
      title: 'Visual Design',
      description: 'Stunning holographic effects with interactive 3D animations',
      features: [
        'Dynamic gradient backgrounds',
        '3D tilt animations',
        'Glassmorphism effects',
        'Smooth color transitions',
        'Interactive lighting'
      ]
    },
    {
      icon: '🔐',
      title: 'Authentication',
      description: 'Complete authentication system with beautiful UI',
      features: [
        'Login & signup forms',
        'Password reset functionality',
        'Real-time validation',
        'Error handling',
        'Loading states'
      ]
    },
    {
      icon: '📱',
      title: 'Responsive Design',
      description: 'Optimized for all devices and screen sizes',
      features: [
        'Mobile-first approach',
        'Touch-friendly interactions',
        'Adaptive layouts',
        'Cross-browser support',
        'High DPI displays'
      ]
    },
    {
      icon: '⚡',
      title: 'Performance',
      description: 'Optimized animations and efficient rendering',
      features: [
        'Hardware acceleration',
        'Efficient re-renders',
        'Minimal bundle impact',
        'Lazy loading',
        'Memory optimization'
      ]
    },
    {
      icon: '♿',
      title: 'Accessibility',
      description: 'Full accessibility support for all users',
      features: [
        'Keyboard navigation',
        'Screen reader support',
        'ARIA labels',
        'Focus management',
        'High contrast mode'
      ]
    },
    {
      icon: '🛠️',
      title: 'Developer Experience',
      description: 'Easy to use and customize with TypeScript support',
      features: [
        'TypeScript definitions',
        'Customizable props',
        'Comprehensive docs',
        'Easy integration',
        'Theme support'
      ]
    }
  ];

  return (
    <div style={{
      background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
      minHeight: '100vh',
      padding: '3rem 2rem',
      color: 'white'
    }}>
      <DemoNavigation currentPage="features" />

      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <div style={{
          textAlign: 'center',
          marginBottom: '4rem'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            margin: '0 0 1rem 0',
            background: 'linear-gradient(135deg, #fff, #a78bfa, #06b6d4)',
            backgroundSize: '200% 200%',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            animation: 'gradient-shift 3s ease-in-out infinite'
          }}>
            ProfileCard Features
          </h1>
          <p style={{
            fontSize: '1.2rem',
            color: 'rgba(255, 255, 255, 0.7)',
            margin: 0,
            maxWidth: '600px',
            marginLeft: 'auto',
            marginRight: 'auto'
          }}>
            Discover the powerful features that make ProfileCard the perfect choice 
            for modern authentication and profile interfaces
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          {features.map((feature, index) => (
            <FeatureCard key={index} {...feature} />
          ))}
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '16px',
          padding: '2rem',
          textAlign: 'center'
        }}>
          <h2 style={{
            color: '#a78bfa',
            margin: '0 0 1rem 0',
            fontSize: '2rem'
          }}>
            🚀 Ready to Get Started?
          </h2>
          <p style={{
            color: 'rgba(255, 255, 255, 0.8)',
            margin: '0 0 2rem 0',
            fontSize: '1.1rem'
          }}>
            Experience the ProfileCard authentication UI in action
          </p>
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            <button
              onClick={() => window.location.href = '/auth-demo'}
              style={{
                padding: '1rem 2rem',
                background: 'linear-gradient(135deg, #8b5cf6, #06b6d4)',
                border: 'none',
                borderRadius: '8px',
                color: 'white',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 10px 25px rgba(139, 92, 246, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              🎮 Try Interactive Demo
            </button>
            <button
              onClick={() => window.open('https://github.com/your-repo/profilecard', '_blank')}
              style={{
                padding: '1rem 2rem',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: 'white',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
              }}
            >
              📚 View Documentation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesShowcase;
