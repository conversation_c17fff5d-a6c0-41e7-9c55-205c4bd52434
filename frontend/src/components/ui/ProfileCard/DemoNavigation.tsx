import React from 'react';

interface NavigationProps {
  currentPage?: 'auth-demo' | 'features' | 'home';
}

const DemoNavigation: React.FC<NavigationProps> = ({ currentPage }) => {
  const navItems = [
    {
      id: 'auth-demo',
      label: '🔐 Auth Demo',
      href: '/auth-demo',
      description: 'Interactive authentication demo'
    },
    {
      id: 'features',
      label: '✨ Features',
      href: '/profilecard-features',
      description: 'Comprehensive feature overview'
    },
    {
      id: 'home',
      label: '🏠 Home',
      href: '/',
      description: 'Back to main application'
    }
  ];

  return (
    <nav style={{
      position: 'fixed',
      top: '20px',
      left: '20px',
      background: 'rgba(0, 0, 0, 0.8)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '12px',
      padding: '1rem',
      zIndex: 1000,
      minWidth: '200px'
    }}>
      <h3 style={{
        color: 'white',
        margin: '0 0 1rem 0',
        fontSize: '0.9rem',
        fontWeight: '600'
      }}>
        ProfileCard Demo
      </h3>
      
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '0.5rem'
      }}>
        {navItems.map((item) => (
          <a
            key={item.id}
            href={item.href}
            style={{
              display: 'block',
              padding: '0.75rem',
              background: currentPage === item.id 
                ? 'linear-gradient(135deg, #8b5cf6, #06b6d4)' 
                : 'rgba(255, 255, 255, 0.05)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '8px',
              color: 'white',
              textDecoration: 'none',
              fontSize: '0.85rem',
              fontWeight: '500',
              transition: 'all 0.2s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              if (currentPage !== item.id) {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.transform = 'translateX(2px)';
              }
            }}
            onMouseLeave={(e) => {
              if (currentPage !== item.id) {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'translateX(0)';
              }
            }}
          >
            <div style={{ marginBottom: '0.25rem' }}>
              {item.label}
            </div>
            <div style={{
              fontSize: '0.7rem',
              color: 'rgba(255, 255, 255, 0.6)',
              lineHeight: '1.2'
            }}>
              {item.description}
            </div>
          </a>
        ))}
      </div>

      <div style={{
        marginTop: '1rem',
        padding: '0.75rem',
        background: 'rgba(255, 255, 255, 0.03)',
        border: '1px solid rgba(255, 255, 255, 0.05)',
        borderRadius: '8px'
      }}>
        <div style={{
          fontSize: '0.7rem',
          color: 'rgba(255, 255, 255, 0.5)',
          marginBottom: '0.5rem'
        }}>
          Quick Tips:
        </div>
        <ul style={{
          listStyle: 'none',
          padding: 0,
          margin: 0,
          fontSize: '0.65rem',
          color: 'rgba(255, 255, 255, 0.6)'
        }}>
          <li style={{ marginBottom: '0.25rem' }}>• Hover cards for 3D effects</li>
          <li style={{ marginBottom: '0.25rem' }}>• Try demo credentials</li>
          <li style={{ marginBottom: '0.25rem' }}>• Test on mobile devices</li>
        </ul>
      </div>
    </nav>
  );
};

export default DemoNavigation;
