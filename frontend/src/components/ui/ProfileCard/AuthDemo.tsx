import React, { useState } from 'react';
import ProfileCard from './ProfileCard';

const AuthDemo: React.FC = () => {
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);

  const handleLogin = async (email: string, password: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate login validation
      if (email === '<EMAIL>' && password === 'password123') {
        setUser({
          name: 'Demo User',
          email: email,
          handle: 'demouser',
          title: 'Software Engineer',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        });
        setIsAuthenticated(true);
      } else {
        setAuthError('Invalid email or password. Try <EMAIL> / password123');
      }
    } catch (error) {
      setAuthError('Login failed. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSignup = async (email: string, password: string, name: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate successful signup
      setUser({
        name: name,
        email: email,
        handle: name.toLowerCase().replace(/\s+/g, ''),
        title: 'New Member',
        avatarUrl: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400&h=400&fit=crop&crop=face'
      });
      setIsAuthenticated(true);
    } catch (error) {
      setAuthError('Signup failed. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleForgotPassword = async (email: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setAuthError(''); // Clear any previous errors
      alert(`Password reset link sent to ${email}`);
    } catch (error) {
      setAuthError('Failed to send reset email. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setUser(null);
    setAuthError('');
  };

  const handleContact = () => {
    alert(`Contacting ${user?.name || 'User'}...`);
  };

  if (isAuthenticated && user) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        gap: '2rem',
        padding: '2rem',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)'
      }}>
        <h1 style={{ 
          color: 'white', 
          textAlign: 'center',
          marginBottom: '1rem',
          fontSize: '2rem',
          fontWeight: 'bold'
        }}>
          Welcome, {user.name}!
        </h1>
        
        <ProfileCard
          avatarUrl={user.avatarUrl}
          name={user.name}
          title={user.title}
          handle={user.handle}
          status="Online"
          contactText="Contact"
          showUserInfo={true}
          onContactClick={handleContact}
          enableTilt={true}
        />
        
        <button
          onClick={handleLogout}
          style={{
            padding: '0.75rem 1.5rem',
            background: 'linear-gradient(135deg, #ef4444, #dc2626)',
            border: 'none',
            borderRadius: '8px',
            color: 'white',
            fontSize: '1rem',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 10px 25px rgba(239, 68, 68, 0.3)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'none';
          }}
        >
          Logout
        </button>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      gap: '2rem',
      padding: '2rem',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{ 
          color: 'white', 
          fontSize: '3rem',
          fontWeight: 'bold',
          margin: '0 0 1rem 0',
          background: 'linear-gradient(135deg, #fff, #a78bfa, #06b6d4)',
          backgroundSize: '200% 200%',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
        }}>
          Authentication Demo
        </h1>
        <p style={{ 
          color: 'rgba(255, 255, 255, 0.7)', 
          fontSize: '1.2rem',
          margin: 0
        }}>
          Experience the beautiful authentication UI
        </p>
        <p style={{ 
          color: 'rgba(255, 255, 255, 0.5)', 
          fontSize: '0.9rem',
          margin: '1rem 0 0 0'
        }}>
          Try: <EMAIL> / password123
        </p>
      </div>
      
      <ProfileCard
        isAuthMode={true}
        onLogin={handleLogin}
        onSignup={handleSignup}
        onForgotPassword={handleForgotPassword}
        authLoading={authLoading}
        authError={authError}
        enableTilt={true}
        avatarUrl="" // Not used in auth mode
      />
    </div>
  );
};

export default AuthDemo;
