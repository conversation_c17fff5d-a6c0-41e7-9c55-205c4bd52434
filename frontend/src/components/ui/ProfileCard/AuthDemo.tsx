import React, { useState } from 'react';
import ProfileCard from './ProfileCard';
import DemoNavigation from './DemoNavigation';

interface DemoUser {
  name: string;
  email: string;
  handle: string;
  title: string;
  avatarUrl: string;
}

const AuthDemo: React.FC = () => {
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<DemoUser | null>(null);
  const [demoMode, setDemoMode] = useState<'auth' | 'profile' | 'both'>('auth');
  const [showInstructions, setShowInstructions] = useState(true);

  const handleLogin = async (email: string, password: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate login validation
      if (email === '<EMAIL>' && password === 'password123') {
        setUser({
          name: 'Demo User',
          email: email,
          handle: 'demouser',
          title: 'Software Engineer',
          avatarUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        });
        setIsAuthenticated(true);
      } else {
        setAuthError('Invalid email or password. Try <EMAIL> / password123');
      }
    } catch (error) {
      setAuthError('Login failed. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSignup = async (email: string, password: string, name: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate successful signup
      setUser({
        name: name,
        email: email,
        handle: name.toLowerCase().replace(/\s+/g, ''),
        title: 'New Member',
        avatarUrl: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400&h=400&fit=crop&crop=face'
      });
      setIsAuthenticated(true);
    } catch (error) {
      setAuthError('Signup failed. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleForgotPassword = async (email: string) => {
    setAuthLoading(true);
    setAuthError('');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setAuthError(''); // Clear any previous errors
      alert(`Password reset link sent to ${email}`);
    } catch (error) {
      setAuthError('Failed to send reset email. Please try again.');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setUser(null);
    setAuthError('');
    setDemoMode('auth');
  };

  const handleContact = () => {
    alert(`Contacting ${user?.name || 'User'}...`);
  };

  const switchToProfileDemo = () => {
    setUser({
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      handle: 'sarahj',
      title: 'UX Designer',
      avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face'
    });
    setIsAuthenticated(true);
    setDemoMode('profile');
  };

  const resetDemo = () => {
    setIsAuthenticated(false);
    setUser(null);
    setAuthError('');
    setDemoMode('auth');
  };

  const DemoControls = () => (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: 'rgba(0, 0, 0, 0.8)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '12px',
      padding: '1rem',
      display: 'flex',
      flexDirection: 'column',
      gap: '0.5rem',
      zIndex: 1000,
      minWidth: '200px'
    }}>
      <h3 style={{ color: 'white', margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>Demo Controls</h3>
      <button
        onClick={() => setDemoMode('auth')}
        style={{
          padding: '0.5rem',
          background: demoMode === 'auth' ? 'linear-gradient(135deg, #8b5cf6, #06b6d4)' : 'rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.8rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        🔐 Auth Mode
      </button>
      <button
        onClick={switchToProfileDemo}
        style={{
          padding: '0.5rem',
          background: demoMode === 'profile' ? 'linear-gradient(135deg, #8b5cf6, #06b6d4)' : 'rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.8rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        👤 Profile Mode
      </button>
      <button
        onClick={() => setDemoMode('both')}
        style={{
          padding: '0.5rem',
          background: demoMode === 'both' ? 'linear-gradient(135deg, #8b5cf6, #06b6d4)' : 'rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.8rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        🔄 Both Modes
      </button>
      <button
        onClick={resetDemo}
        style={{
          padding: '0.5rem',
          background: 'rgba(239, 68, 68, 0.2)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: '6px',
          color: '#fca5a5',
          fontSize: '0.8rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        🔄 Reset Demo
      </button>
      <button
        onClick={() => setShowInstructions(!showInstructions)}
        style={{
          padding: '0.5rem',
          background: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          color: 'white',
          fontSize: '0.8rem',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
      >
        {showInstructions ? '🙈 Hide Info' : '👁️ Show Info'}
      </button>
    </div>
  );

  if (isAuthenticated && user && demoMode === 'profile') {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '2rem',
        padding: '2rem',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
        position: 'relative'
      }}>
        <DemoControls />

        <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
          <h1 style={{
            color: 'white',
            fontSize: '2.5rem',
            fontWeight: 'bold',
            margin: '0 0 0.5rem 0',
            background: 'linear-gradient(135deg, #fff, #a78bfa, #06b6d4)',
            backgroundSize: '200% 200%',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
          }}>
            Welcome, {user.name}!
          </h1>
          <p style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '1.1rem',
            margin: 0
          }}>
            Profile Card Demo - Interactive 3D Card
          </p>
        </div>

        <ProfileCard
          avatarUrl={user.avatarUrl}
          name={user.name}
          title={user.title}
          handle={user.handle}
          status="Online"
          contactText="Contact"
          showUserInfo={true}
          onContactClick={handleContact}
          enableTilt={true}
        />

        {showInstructions && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '1.5rem',
            maxWidth: '500px',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#a78bfa', margin: '0 0 1rem 0' }}>✨ Interactive Features</h3>
            <ul style={{
              color: 'rgba(255, 255, 255, 0.8)',
              textAlign: 'left',
              listStyle: 'none',
              padding: 0,
              margin: 0
            }}>
              <li style={{ marginBottom: '0.5rem' }}>🖱️ Hover over the card to see 3D tilt effects</li>
              <li style={{ marginBottom: '0.5rem' }}>✨ Watch the holographic background animations</li>
              <li style={{ marginBottom: '0.5rem' }}>👆 Click the Contact button to interact</li>
              <li style={{ marginBottom: '0.5rem' }}>📱 Try on mobile for touch interactions</li>
            </ul>
          </div>
        )}
      </div>
    );
  }

  if (demoMode === 'both') {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '3rem',
        padding: '2rem',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
        position: 'relative'
      }}>
        <DemoControls />

        <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
          <h1 style={{
            color: 'white',
            fontSize: '3rem',
            fontWeight: 'bold',
            margin: '0 0 1rem 0',
            background: 'linear-gradient(135deg, #fff, #a78bfa, #06b6d4)',
            backgroundSize: '200% 200%',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
          }}>
            ProfileCard Showcase
          </h1>
          <p style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '1.2rem',
            margin: 0
          }}>
            Authentication & Profile Modes Side by Side
          </p>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '3rem',
          width: '100%',
          maxWidth: '1200px'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h2 style={{
              color: '#a78bfa',
              marginBottom: '1rem',
              fontSize: '1.5rem'
            }}>
              🔐 Authentication Mode
            </h2>
            <ProfileCard
              isAuthMode={true}
              onLogin={handleLogin}
              onSignup={handleSignup}
              onForgotPassword={handleForgotPassword}
              authLoading={authLoading}
              authError={authError}
              enableTilt={true}
              avatarUrl=""
            />
          </div>

          <div style={{ textAlign: 'center' }}>
            <h2 style={{
              color: '#06b6d4',
              marginBottom: '1rem',
              fontSize: '1.5rem'
            }}>
              👤 Profile Mode
            </h2>
            <ProfileCard
              avatarUrl="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
              name="Alex Chen"
              title="Full Stack Developer"
              handle="alexchen"
              status="Online"
              contactText="Contact"
              showUserInfo={true}
              onContactClick={() => alert('Contacting Alex Chen...')}
              enableTilt={true}
            />
          </div>
        </div>

        {showInstructions && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '2rem',
            maxWidth: '800px',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#a78bfa', margin: '0 0 1.5rem 0' }}>🚀 Demo Instructions</h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '2rem',
              textAlign: 'left'
            }}>
              <div>
                <h4 style={{ color: '#06b6d4', marginBottom: '0.5rem' }}>Authentication Card</h4>
                <ul style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  listStyle: 'none',
                  padding: 0,
                  margin: 0
                }}>
                  <li style={{ marginBottom: '0.5rem' }}>📧 Email: <EMAIL></li>
                  <li style={{ marginBottom: '0.5rem' }}>🔑 Password: password123</li>
                  <li style={{ marginBottom: '0.5rem' }}>🔄 Try signup and forgot password</li>
                  <li style={{ marginBottom: '0.5rem' }}>👁️ Toggle password visibility</li>
                </ul>
              </div>
              <div>
                <h4 style={{ color: '#06b6d4', marginBottom: '0.5rem' }}>Profile Card</h4>
                <ul style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  listStyle: 'none',
                  padding: 0,
                  margin: 0
                }}>
                  <li style={{ marginBottom: '0.5rem' }}>🖱️ Hover for 3D tilt effects</li>
                  <li style={{ marginBottom: '0.5rem' }}>✨ Interactive holographic background</li>
                  <li style={{ marginBottom: '0.5rem' }}>👆 Click contact button</li>
                  <li style={{ marginBottom: '0.5rem' }}>📱 Touch-friendly on mobile</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '2rem',
      padding: '2rem',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
      position: 'relative'
    }}>
      <DemoNavigation currentPage="auth-demo" />
      <DemoControls />

      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{
          color: 'white',
          fontSize: '3rem',
          fontWeight: 'bold',
          margin: '0 0 1rem 0',
          background: 'linear-gradient(135deg, #fff, #a78bfa, #06b6d4)',
          backgroundSize: '200% 200%',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
        }}>
          Authentication Demo
        </h1>
        <p style={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: '1.2rem',
          margin: 0
        }}>
          Experience the beautiful authentication UI
        </p>
        {showInstructions && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '8px',
            padding: '1rem',
            margin: '1rem 0',
            maxWidth: '400px',
            marginLeft: 'auto',
            marginRight: 'auto'
          }}>
            <p style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '0.9rem',
              margin: '0 0 0.5rem 0',
              fontWeight: '600'
            }}>
              🚀 Try the demo:
            </p>
            <p style={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '0.8rem',
              margin: 0
            }}>
              📧 <EMAIL><br />
              🔑 password123
            </p>
          </div>
        )}
      </div>

      <ProfileCard
        isAuthMode={true}
        onLogin={handleLogin}
        onSignup={handleSignup}
        onForgotPassword={handleForgotPassword}
        authLoading={authLoading}
        authError={authError}
        enableTilt={true}
        avatarUrl="" // Not used in auth mode
      />

      {showInstructions && (
        <div style={{
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          padding: '1.5rem',
          maxWidth: '500px',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#a78bfa', margin: '0 0 1rem 0' }}>✨ Features to Try</h3>
          <ul style={{
            color: 'rgba(255, 255, 255, 0.8)',
            textAlign: 'left',
            listStyle: 'none',
            padding: 0,
            margin: 0
          }}>
            <li style={{ marginBottom: '0.5rem' }}>🔄 Switch between Login, Signup, and Forgot Password</li>
            <li style={{ marginBottom: '0.5rem' }}>👁️ Toggle password visibility</li>
            <li style={{ marginBottom: '0.5rem' }}>🖱️ Hover over the card for 3D tilt effects</li>
            <li style={{ marginBottom: '0.5rem' }}>✨ Watch the holographic background animations</li>
            <li style={{ marginBottom: '0.5rem' }}>📱 Try on mobile for touch interactions</li>
            <li style={{ marginBottom: '0.5rem' }}>⚡ Experience loading states and error handling</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default AuthDemo;
