<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProfileCard Authentication UI Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: white;
        }

        .demo-container {
            text-align: center;
            max-width: 800px;
            width: 100%;
        }

        .demo-header {
            margin-bottom: 3rem;
        }

        .demo-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #fff, #a78bfa, #06b6d4);
            background-size: 200% 200%;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
            animation: gradient-shift 3s ease-in-out infinite;
        }

        .demo-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 1rem;
        }

        .demo-instructions {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.05);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-preview {
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            aspect-ratio: 0.718;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            border-radius: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .card-preview:hover {
            transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
        }

        .card-inner {
            position: absolute;
            inset: 2px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 28px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
        }

        .auth-form-preview {
            width: 100%;
            max-width: 300px;
        }

        .form-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #fff, #a78bfa);
            -webkit-text-fill-color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
        }

        .form-subtitle {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-label {
            display: block;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: white;
            font-size: 0.9rem;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        .submit-btn {
            width: 100%;
            padding: 0.875rem;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .tech-stack {
            margin-top: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tech-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #a78bfa;
        }

        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            text-align: left;
        }

        .tech-item {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .tech-item h4 {
            color: #06b6d4;
            margin-bottom: 0.5rem;
        }

        .tech-item p {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @media (max-width: 768px) {
            .demo-title { font-size: 2rem; }
            .demo-subtitle { font-size: 1rem; }
            .card-preview { max-width: 300px; }
            .form-title { font-size: 1.5rem; }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">ProfileCard Authentication UI</h1>
            <p class="demo-subtitle">Beautiful, Interactive Authentication Interface</p>
            <div class="demo-instructions">
                <strong>🚀 Live Demo Available:</strong> Visit <code>/auth-demo</code> in your React app<br>
                <strong>📧 Test Credentials:</strong> <EMAIL> / password123
            </div>
        </div>

        <div class="card-preview">
            <div class="card-inner">
                <div class="auth-form-preview">
                    <h2 class="form-title">Welcome Back</h2>
                    <p class="form-subtitle">Sign in to your account</p>
                    
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-input" placeholder="Enter your email" value="<EMAIL>" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-input" placeholder="Enter your password" value="password123" readonly>
                    </div>
                    
                    <button class="submit-btn">Sign In</button>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h3 class="tech-title">✨ Features & Technologies</h3>
            <div class="tech-list">
                <div class="tech-item">
                    <h4>🎨 Visual Effects</h4>
                    <p>Holographic gradients, 3D tilt animations, glassmorphism effects</p>
                </div>
                <div class="tech-item">
                    <h4>🔐 Authentication</h4>
                    <p>Login, signup, password reset with validation and error handling</p>
                </div>
                <div class="tech-item">
                    <h4>📱 Responsive</h4>
                    <p>Optimized for mobile, tablet, and desktop with touch support</p>
                </div>
                <div class="tech-item">
                    <h4>⚡ Performance</h4>
                    <p>Optimized animations, efficient re-renders, minimal bundle impact</p>
                </div>
                <div class="tech-item">
                    <h4>♿ Accessibility</h4>
                    <p>Keyboard navigation, ARIA labels, screen reader support</p>
                </div>
                <div class="tech-item">
                    <h4>🛠️ Developer Experience</h4>
                    <p>TypeScript, customizable props, comprehensive documentation</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
