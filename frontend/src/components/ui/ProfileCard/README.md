# ProfileCard Authentication UI

A beautiful, interactive authentication interface built on top of the ProfileCard component with stunning visual effects and smooth animations.

## Features

### 🎨 Visual Design
- **Holographic Effects**: Dynamic gradient backgrounds with interactive lighting
- **3D Tilt Animation**: Mouse-responsive card tilting with perspective transforms
- **Glassmorphism**: Frosted glass effects with backdrop blur
- **Animated Gradients**: Smooth color transitions and gradient animations
- **Responsive Design**: Optimized for all screen sizes from mobile to desktop

### 🔐 Authentication Modes
- **Login**: Email/password authentication with validation
- **Signup**: User registration with name, email, and password confirmation
- **Forgot Password**: Password reset functionality
- **Loading States**: Beautiful loading animations during API calls
- **Error Handling**: Elegant error message display

### ⚡ Interactive Features
- **Password Visibility Toggle**: Show/hide password with animated icons
- **Form Validation**: Real-time input validation and feedback
- **Smooth Transitions**: Seamless switching between auth modes
- **Keyboard Navigation**: Full accessibility support
- **Touch Optimized**: Perfect for mobile and tablet devices

## Usage

### Basic Authentication Mode

```tsx
import { ProfileCard } from './components/ui/ProfileCard';

function AuthPage() {
  const [authLoading, setAuthLoading] = useState(false);
  const [authError, setAuthError] = useState('');

  const handleLogin = async (email: string, password: string) => {
    setAuthLoading(true);
    try {
      // Your login logic here
      await loginUser(email, password);
    } catch (error) {
      setAuthError('Invalid credentials');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSignup = async (email: string, password: string, name: string) => {
    setAuthLoading(true);
    try {
      // Your signup logic here
      await registerUser(email, password, name);
    } catch (error) {
      setAuthError('Registration failed');
    } finally {
      setAuthLoading(false);
    }
  };

  const handleForgotPassword = async (email: string) => {
    setAuthLoading(true);
    try {
      // Your password reset logic here
      await sendPasswordReset(email);
    } catch (error) {
      setAuthError('Failed to send reset email');
    } finally {
      setAuthLoading(false);
    }
  };

  return (
    <ProfileCard
      isAuthMode={true}
      onLogin={handleLogin}
      onSignup={handleSignup}
      onForgotPassword={handleForgotPassword}
      authLoading={authLoading}
      authError={authError}
      enableTilt={true}
      avatarUrl="" // Not used in auth mode
    />
  );
}
```

### Profile Display Mode

```tsx
<ProfileCard
  avatarUrl="https://example.com/avatar.jpg"
  name="John Doe"
  title="Software Engineer"
  handle="johndoe"
  status="Online"
  contactText="Contact"
  showUserInfo={true}
  onContactClick={() => console.log('Contact clicked')}
  enableTilt={true}
/>
```

## Props

### Authentication Props
- `isAuthMode?: boolean` - Enable authentication UI mode
- `onLogin?: (email: string, password: string) => void` - Login handler
- `onSignup?: (email: string, password: string, name: string) => void` - Signup handler
- `onForgotPassword?: (email: string) => void` - Forgot password handler
- `authLoading?: boolean` - Loading state for auth operations
- `authError?: string` - Error message to display

### Profile Props
- `avatarUrl: string` - User avatar image URL
- `name?: string` - User's display name
- `title?: string` - User's job title or role
- `handle?: string` - User's handle/username
- `status?: string` - User's online status
- `contactText?: string` - Contact button text
- `showUserInfo?: boolean` - Show user info overlay
- `onContactClick?: () => void` - Contact button handler

### Visual Props
- `enableTilt?: boolean` - Enable 3D tilt animation
- `showBehindGradient?: boolean` - Show background gradient effects
- `behindGradient?: string` - Custom background gradient
- `innerGradient?: string` - Custom inner gradient
- `className?: string` - Additional CSS classes

## Demo

Visit `/auth-demo` to see the authentication UI in action with:
- Interactive login/signup forms
- Real-time validation
- Loading states and error handling
- Smooth animations and transitions
- Responsive design across all devices

### Demo Credentials
- Email: `<EMAIL>`
- Password: `password123`

## Styling

The component uses CSS custom properties for easy theming:

```css
:root {
  --card-radius: 30px;
  --sunpillar-1: hsl(2, 100%, 73%);
  --sunpillar-2: hsl(53, 100%, 69%);
  /* ... more color variables */
}
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Screen reader compatible
- High contrast mode support
- Focus management

## Performance

- Optimized animations with `transform3d`
- Efficient re-renders with React.memo
- Lazy loading for images
- Minimal bundle impact
