# Milvus Configuration
etcd:
  endpoints:
    - etcd:2379

minio:
  address: minio
  port: 9000
  accessKeyID: minioadmin
  secretAccessKey: minioadmin
  useSSL: false
  bucketName: "milvus-bucket"

common:
  defaultPartitionName: "_default"
  defaultIndexName: "_default_idx"
  retentionDuration: 432000
  indexSliceSize: 16
  gracefulTime: 0
  gracefulStopTimeout: 30

storage:
  path: /var/lib/milvus

log:
  level: info
  format: text
  path: /var/lib/milvus/logs

dataNode:
  dataSync:
    flowGraph:
      maxQueueLength: 1024
      maxParallelism: 1024

queryNode:
  scheduler:
    receiveChanSize: 10240
    unsolvedQueueSize: 10240
    maxReadConcurrency: 16
  gracefulStopTimeout: 30

queryCoord:
  overloadedMemoryThresholdPercentage: 90
  memoryUsageMaxDifferencePercentage: 30
  checkInterval: 1000
  channelTaskTimeout: 60000
  segmentTaskTimeout: 120000
  loadTimeoutSeconds: 600

rootCoord:
  maxPartitionNum: 4096
  minSegmentSizeToEnableIndex: 1024
  importTaskExpiration: 900
  importTaskRetention: 86400
  importMaxFileSize: 17179869184