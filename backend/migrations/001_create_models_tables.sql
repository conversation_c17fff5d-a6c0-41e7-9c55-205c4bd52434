-- Create model type enum
CREATE TYPE model_type AS ENUM ('embedding', 'llm', 'classification', 'generation', 'other');

-- Create model status enum
CREATE TYPE model_status AS ENUM ('idle', 'loading', 'running', 'error');

-- Create training status enum  
CREATE TYPE training_status AS ENUM ('pending', 'running', 'completed', 'failed');

-- Create ai_models table
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type model_type NOT NULL,
    version VARCHAR(50) NOT NULL,
    status model_status NOT NULL DEFAULT 'idle',
    accuracy DECIMAL(5,4),
    last_trained TIMESTAMP,
    dataset_size INTEGER,
    framework VARCHAR(100) NOT NULL,
    size VARCHAR(50) NOT NULL,
    parameters VARCHAR(50),
    description TEXT,
    docker_image VARCHAR(255),
    container_id VARCHAR(255),
    model_path VARCHAR(500),
    config J<PERSON><PERSON><PERSON>,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create training_jobs table
CREATE TABLE training_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID REFERENCES ai_models(id) NOT NULL,
    status training_status NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    epochs INTEGER NOT NULL,
    current_epoch INTEGER NOT NULL DEFAULT 0,
    loss DECIMAL(10,6),
    accuracy DECIMAL(5,4),
    batch_size INTEGER,
    learning_rate DECIMAL(8,6),
    dataset_path VARCHAR(500),
    config JSONB,
    logs TEXT,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create model_metrics table
CREATE TABLE model_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID REFERENCES ai_models(id) NOT NULL,
    accuracy DECIMAL(5,4) NOT NULL,
    precision DECIMAL(5,4) NOT NULL,
    recall DECIMAL(5,4) NOT NULL,
    f1_score DECIMAL(5,4) NOT NULL,
    latency INTEGER NOT NULL,
    throughput INTEGER NOT NULL,
    memory_usage DECIMAL(8,2) NOT NULL,
    cpu_usage DECIMAL(5,2) NOT NULL,
    timestamp TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_ai_models_status ON ai_models(status);
CREATE INDEX idx_ai_models_type ON ai_models(type);
CREATE INDEX idx_training_jobs_model_id ON training_jobs(model_id);
CREATE INDEX idx_training_jobs_status ON training_jobs(status);
CREATE INDEX idx_model_metrics_model_id ON model_metrics(model_id);
CREATE INDEX idx_model_metrics_timestamp ON model_metrics(timestamp);

-- Insert some sample data
INSERT INTO ai_models (id, name, type, version, status, accuracy, last_trained, dataset_size, framework, size, parameters, description, docker_image, model_path) VALUES 
('************************************', 'BERT Base Embeddings', 'embedding', '1.2.0', 'idle', 0.94, '2024-01-15', 100000, 'PyTorch', '420MB', '110M', 'BERT base model for text embeddings', 'pytorch/pytorch:latest', '/models/bert-base'),
('550e8400-e29b-41d4-a716-446655440002', 'GPT-3.5 Turbo', 'llm', '2.1.0', 'idle', 0.89, '2024-01-10', 500000, 'TensorFlow', '2.1GB', '175B', 'Large language model for text generation', 'tensorflow/serving:latest', '/models/gpt-3.5'),
('550e8400-e29b-41d4-a716-446655440003', 'Sentiment Classifier', 'classification', '1.0.0', 'idle', 0.87, '2024-01-08', 50000, 'scikit-learn', '15MB', '1M', 'Binary sentiment classification model', 'python:3.9-slim', '/models/sentiment'),
('550e8400-e29b-41d4-a716-446655440004', 'Stable Diffusion', 'generation', '1.5.0', 'idle', 0.91, '2024-01-12', 1000000, 'PyTorch', '4.2GB', '860M', 'Text-to-image generation model', 'pytorch/pytorch:latest', '/models/stable-diffusion');

-- Insert sample training jobs
INSERT INTO training_jobs (id, model_id, status, progress, start_time, epochs, current_epoch, loss, accuracy, batch_size, learning_rate) VALUES 
('650e8400-e29b-41d4-a716-446655440001', '************************************', 'completed', 100, '2024-01-15 10:00:00', 10, 10, 0.15, 0.94, 32, 0.001),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'completed', 100, '2024-01-08 08:00:00', 20, 20, 0.12, 0.87, 64, 0.0005);

-- Insert sample metrics
INSERT INTO model_metrics (model_id, accuracy, precision, recall, f1_score, latency, throughput, memory_usage, cpu_usage) VALUES 
('************************************', 0.94, 0.92, 0.89, 0.90, 45, 1200, 2.1, 35),
('550e8400-e29b-41d4-a716-446655440002', 0.89, 0.87, 0.85, 0.86, 120, 800, 8.5, 65),
('550e8400-e29b-41d4-a716-446655440003', 0.87, 0.85, 0.82, 0.83, 25, 2000, 0.5, 15),
('550e8400-e29b-41d4-a716-446655440004', 0.91, 0.88, 0.86, 0.87, 2500, 10, 12.0, 85);
