{"name": "@deno/shim-deno-test", "version": "0.5.0", "description": "Deno.test only shim.", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npm run generate-deno-types && tsc", "generate-deno-types": "deno run --allow-read --allow-write=. ./scripts/generateDenoTypes.ts", "test": "echo 'tested by shim-deno package.'"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/denoland/node_deno_shims.git"}, "keywords": ["shim", "deno", "test", "node.js"], "author": "The Deno authors", "license": "MIT", "bugs": {"url": "https://github.com/denoland/node_deno_shims/issues"}, "homepage": "https://github.com/denoland/node_deno_shims#readme", "devDependencies": {"typescript": "^5.2.2"}}