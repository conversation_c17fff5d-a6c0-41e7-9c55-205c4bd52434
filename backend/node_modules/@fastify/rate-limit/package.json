{"name": "@fastify/rate-limit", "version": "10.3.0", "description": "A low overhead rate limiter for your routes", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "redis": "docker run -p 6379:6379 --name rate-limit-redis -d --rm redis", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 --100 node --test", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-rate-limit.git"}, "keywords": ["fastify", "rate", "limit"], "author": "<PERSON> - @delvedor (http://delved.org)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-rate-limit/issues"}, "homepage": "https://github.com/fastify/fastify-rate-limit#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@sinonjs/fake-timers": "^14.0.0", "@types/node": "^22.0.0", "c8": "^10.1.2", "eslint": "^9.17.0", "fastify": "^5.0.0", "ioredis": "^5.4.1", "knex": "^3.1.0", "neostandard": "^0.12.0", "sqlite3": "^5.1.7", "tsd": "^0.32.0"}, "dependencies": {"@lukeed/ms": "^2.0.2", "fastify-plugin": "^5.0.0", "toad-cache": "^3.7.0"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}