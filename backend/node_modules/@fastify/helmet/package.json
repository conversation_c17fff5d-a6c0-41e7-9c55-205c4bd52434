{"name": "@fastify/helmet", "version": "12.0.1", "description": "Important security headers for Fastify", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"coverage": "npm run unit -- --coverage-report=lcovonly", "lint": "standard | snazzy", "lint:fix": "standard --fix | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "test:ci": "npm run lint && npm run coverage && npm run typescript", "unit": "c8 --100 node --test", "unit:report": "npm run unit -- --coverage-report=html", "unit:verbose": "npm run unit -- -Rspec", "typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-helmet.git"}, "keywords": ["fastify", "helmet", "security", "headers", "x-frame-options", "csp", "hsts", "clickjack"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-helmet/issues"}, "homepage": "https://github.com/fastify/fastify-helmet#readme", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@types/node": "^22.0.0", "c8": "^10.1.2", "fastify": "^5.0.0", "snazzy": "^9.0.0", "standard": "^17.1.0", "tsd": "^0.31.0"}, "dependencies": {"fastify-plugin": "^5.0.0", "helmet": "^7.1.0"}, "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}