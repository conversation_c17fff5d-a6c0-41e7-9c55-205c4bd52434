{"name": "@fastify/cookie", "version": "10.0.1", "description": "Plugin for fastify to add support for cookies", "main": "plugin.js", "type": "commonjs", "types": "types/plugin.d.ts", "scripts": {"coverage": "npm run test:unit -- --coverage-report=html", "lint": "standard | snazzy", "lint:ci": "standard", "lint:fix": "standard --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "tap", "test:unit:verbose": "npm run test:unit -- -Rspec"}, "precommit": ["lint", "test"], "repository": {"type": "git", "url": "git+ssh://**************/fastify/fastify-cookie.git"}, "keywords": ["fastify", "cookie"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-cookie/issues"}, "homepage": "https://github.com/fastify/fastify-cookie#readme", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@types/node": "^22.0.0", "benchmark": "^2.1.4", "fastify": "^5.0.0", "sinon": "^19.0.2", "snazzy": "^9.0.0", "standard": "^17.1.0", "tap": "^18.6.1", "tsd": "^0.31.1"}, "dependencies": {"fastify-plugin": "^5.0.0", "cookie-signature": "^1.2.1"}, "tsd": {"directory": "test"}, "publishConfig": {"access": "public"}}