import si from 'systeminformation';
export class SystemMonitorService {
    static instance;
    networkStats = null;
    static getInstance() {
        if (!SystemMonitorService.instance) {
            SystemMonitorService.instance = new SystemMonitorService();
        }
        return SystemMonitorService.instance;
    }
    async getSystemMetrics() {
        try {
            const [cpuLoad, cpuTemp, cpuInfo, memory, diskUsage, networkStats, osInfo, uptime] = await Promise.all([
                si.currentLoad(),
                si.cpuTemperature().catch(() => null),
                si.cpu(),
                si.mem(),
                si.fsSize(),
                si.networkStats(),
                si.osInfo(),
                si.time()
            ]);
            // Store network stats for rate calculation
            const currentTime = Date.now();
            let networkIn = 0;
            let networkOut = 0;
            let packetsIn = 0;
            let packetsOut = 0;
            if (networkStats && networkStats.length > 0) {
                networkStats.forEach((stat) => {
                    networkIn += stat.rx_bytes || 0;
                    networkOut += stat.tx_bytes || 0;
                    packetsIn += stat.rx_packets || 0;
                    packetsOut += stat.tx_packets || 0;
                });
            }
            // Calculate disk usage (use the primary disk)
            const primaryDisk = diskUsage.find((disk) => disk.mount === '/') || diskUsage[0];
            const diskTotal = primaryDisk?.size || 0;
            const diskUsed = primaryDisk?.used || 0;
            const diskAvailable = primaryDisk?.available || 0;
            return {
                cpu: {
                    usage: Math.round(cpuLoad.currentLoad * 100) / 100,
                    temperature: cpuTemp?.main || undefined,
                    cores: cpuInfo.cores
                },
                memory: {
                    total: memory.total,
                    used: memory.used,
                    available: memory.available,
                    percentage: Math.round((memory.used / memory.total) * 100 * 100) / 100
                },
                disk: {
                    total: diskTotal,
                    used: diskUsed,
                    available: diskAvailable,
                    percentage: diskTotal > 0 ? Math.round((diskUsed / diskTotal) * 100 * 100) / 100 : 0
                },
                network: {
                    bytesIn: networkIn,
                    bytesOut: networkOut,
                    packetsIn,
                    packetsOut
                },
                system: {
                    uptime: uptime.uptime,
                    platform: osInfo.platform,
                    arch: osInfo.arch,
                    hostname: osInfo.hostname
                },
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Error getting system metrics:', error);
            throw new Error('Failed to retrieve system metrics');
        }
    }
    async checkServiceHealth(services) {
        const healthChecks = services.map(async (service) => {
            const startTime = Date.now();
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), service.timeout || 5000);
                const response = await fetch(service.url, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: {
                        'User-Agent': 'System-Monitor/1.0'
                    }
                });
                clearTimeout(timeoutId);
                const responseTime = Date.now() - startTime;
                return {
                    name: service.name,
                    status: response.ok ? 'healthy' : 'unhealthy',
                    url: service.url,
                    responseTime,
                    lastCheck: new Date().toISOString(),
                    error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
                };
            }
            catch (error) {
                const responseTime = Date.now() - startTime;
                return {
                    name: service.name,
                    status: 'unhealthy',
                    url: service.url,
                    responseTime,
                    lastCheck: new Date().toISOString(),
                    error: error instanceof Error ? error.message : 'Unknown error'
                };
            }
        });
        return Promise.all(healthChecks);
    }
    async getProcessList() {
        try {
            const processes = await si.processes();
            return processes.list
                .filter((proc) => proc.cpu > 0.1) // Only show processes using > 0.1% CPU
                .sort((a, b) => b.cpu - a.cpu) // Sort by CPU usage
                .slice(0, 10) // Top 10 processes
                .map((proc) => ({
                pid: proc.pid,
                name: proc.name,
                cpu: Math.round(proc.cpu * 100) / 100,
                memory: Math.round(proc.memRss / 1024 / 1024 * 100) / 100, // Convert to MB
                command: proc.command
            }));
        }
        catch (error) {
            console.error('Error getting process list:', error);
            return [];
        }
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        }
        else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        }
        else {
            return `${minutes}m`;
        }
    }
}
export const systemMonitor = SystemMonitorService.getInstance();
//# sourceMappingURL=system-monitor.js.map