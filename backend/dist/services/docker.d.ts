export interface ContainerInfo {
    id: string;
    name: string;
    image: string;
    status: 'running' | 'stopped' | 'error' | 'exited' | 'created' | 'restarting';
    state: string;
    ports: Array<{
        privatePort: number;
        publicPort?: number;
        type: string;
    }>;
    created: string;
    uptime?: string;
    stats?: {
        cpuUsage: number;
        memoryUsage: number;
        memoryLimit: number;
        networkRx: number;
        networkTx: number;
    };
}
export declare class DockerService {
    private docker;
    private projectRoot;
    constructor();
    isDockerRunning(): Promise<boolean>;
    listContainers(all?: boolean): Promise<ContainerInfo[]>;
    getContainer(id: string): Promise<ContainerInfo | null>;
    startContainer(id: string): Promise<boolean>;
    stopContainer(id: string): Promise<boolean>;
    restartContainer(id: string): Promise<boolean>;
    getContainerLogs(id: string, tail?: number): Promise<string>;
    removeContainer(id: string): Promise<boolean>;
    createAndStartContainer(config: any): Promise<string>;
    executeDockerCompose(command: string, options?: Record<string, any>): Promise<string>;
    getDockerComposeServices(): Promise<Array<{
        name: string;
        image: string;
        status: string;
        ports: string[];
    }>>;
    buildService(serviceName?: string, options?: {
        noCache?: boolean;
    }): Promise<string>;
    pullImages(serviceName?: string): Promise<string>;
    getComposeStatus(): Promise<{
        isRunning: boolean;
        services: number;
        runningServices: number;
        containers: Array<{
            name: string;
            service: string;
            status: string;
            health?: string;
        }>;
    }>;
    getImages(): Promise<Array<{
        id: string;
        repository: string;
        tag: string;
        size: number;
        created: string;
    }>>;
    removeImage(imageId: string, force?: boolean): Promise<boolean>;
    getNetworks(): Promise<Array<{
        id: string;
        name: string;
        driver: string;
        scope: string;
        created: string;
        containers: number;
    }>>;
    getVolumes(): Promise<Array<{
        name: string;
        driver: string;
        mountpoint: string;
        created: string;
        size?: number;
    }>>;
    removeVolume(volumeName: string, force?: boolean): Promise<boolean>;
    getSystemDf(): Promise<{
        containers: {
            active: number;
            total: number;
            size: number;
            reclaimable: number;
        };
        images: {
            active: number;
            total: number;
            size: number;
            reclaimable: number;
        };
        volumes: {
            active: number;
            total: number;
            size: number;
            reclaimable: number;
        };
    }>;
    private mapContainerStatus;
    private calculateUptime;
    private parseContainerStats;
    private extractServiceName;
    private getContainerHealth;
}
export declare const dockerService: DockerService;
//# sourceMappingURL=docker.d.ts.map