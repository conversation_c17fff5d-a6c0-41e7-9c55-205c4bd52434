{"version": 3, "file": "system-monitor.js", "sourceRoot": "", "sources": ["../../src/services/system-monitor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,mBAAmB,CAAC;AA4CnC,MAAM,OAAO,oBAAoB;IACvB,MAAM,CAAC,QAAQ,CAAuB;IACtC,YAAY,GAAQ,IAAI,CAAC;IAEjC,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;YACnC,oBAAoB,CAAC,QAAQ,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC7D,CAAC;QACD,OAAO,oBAAoB,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YAAQ,MAAM,CACf,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,SAAS,EACT,YAAY,EACZ,MAAM,EACN,MAAM,CACP,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,EAAE,CAAC,WAAW,EAAE;gBAChB,EAAE,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;gBACrC,EAAE,CAAC,GAAG,EAAE;gBACR,EAAE,CAAC,GAAG,EAAE;gBACR,EAAE,CAAC,MAAM,EAAE;gBACX,EAAE,CAAC,YAAY,EAAE;gBACjB,EAAE,CAAC,MAAM,EAAE;gBACX,EAAE,CAAC,IAAI,EAAE;aACV,CAAC,CAAC;YAEH,2CAA2C;YAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC/B,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,YAAY,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;oBACjC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;oBAChC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACjC,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;oBAClC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;YACL,CAAC;YAED,8CAA8C;YAC9C,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;YACtF,MAAM,SAAS,GAAG,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,WAAW,EAAE,SAAS,IAAI,CAAC,CAAC;YAElD,OAAO;gBACL,GAAG,EAAE;oBACH,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG;oBAClD,WAAW,EAAE,OAAO,EAAE,IAAI,IAAI,SAAS;oBACvC,KAAK,EAAE,OAAO,CAAC,KAAK;iBACrB;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;iBACvE;gBACD,IAAI,EAAE;oBACJ,KAAK,EAAE,SAAS;oBAChB,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,aAAa;oBACxB,UAAU,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBACrF;gBACD,OAAO,EAAE;oBACP,OAAO,EAAE,SAAS;oBAClB,QAAQ,EAAE,UAAU;oBACpB,SAAS;oBACT,UAAU;iBACX;gBACD,MAAM,EAAE;oBACN,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAgE;QACvF,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAClD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;gBACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;gBAEhF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;oBACxC,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACP,YAAY,EAAE,oBAAoB;qBACnC;iBACF,CAAC,CAAC;gBAEH,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAE5C,OAAO;oBACL,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;oBAC7C,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,YAAY;oBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE;iBAClE,CAAC;YACrB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC5C,OAAO;oBACL,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,MAAM,EAAE,WAAW;oBACnB,GAAG,EAAE,OAAO,CAAC,GAAG;oBAChB,YAAY;oBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAC/C,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,cAAc;QAOlB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,SAAS,EAAE,CAAC;YACvC,OAAO,SAAS,CAAC,IAAI;iBAClB,MAAM,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uCAAuC;iBAC7E,IAAI,CAAC,CAAC,CAAM,EAAE,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,oBAAoB;iBAC5D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,mBAAmB;iBAChC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACnB,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;gBACrC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,EAAE,gBAAgB;gBAC3E,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC,CAAC;QACR,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,WAAW,CAAC,KAAa;QACvB,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC9B,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,YAAY,CAAC,OAAe;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAElD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,OAAO,GAAG,IAAI,KAAK,KAAK,KAAK,OAAO,GAAG,CAAC;QAC1C,CAAC;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,CAAC;IACH,CAAC;CACF;AAED,MAAM,CAAC,MAAM,aAAa,GAAG,oBAAoB,CAAC,WAAW,EAAE,CAAC"}