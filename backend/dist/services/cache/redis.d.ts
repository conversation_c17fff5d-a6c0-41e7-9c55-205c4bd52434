import { RedisClientType } from 'redis';
declare class RedisService {
    private client;
    private connected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    del(key: string): Promise<number>;
    exists(key: string): Promise<number>;
    getJson<T>(key: string): Promise<T | null>;
    setJson<T>(key: string, value: T, ttl?: number): Promise<void>;
    hGet(key: string, field: string): Promise<string | undefined>;
    hSet(key: string, field: string, value: string): Promise<number>;
    hGetAll(key: string): Promise<Record<string, string>>;
    hDel(key: string, field: string): Promise<number>;
    lPush(key: string, value: string): Promise<number>;
    rPush(key: string, value: string): Promise<number>;
    lPop(key: string): Promise<string | null>;
    rPop(key: string): Promise<string | null>;
    lRange(key: string, start: number, stop: number): Promise<string[]>;
    sAdd(key: string, value: string): Promise<number>;
    sRem(key: string, value: string): Promise<number>;
    sMembers(key: string): Promise<string[]>;
    sIsMember(key: string, value: string): Promise<boolean>;
    keys(pattern: string): Promise<string[]>;
    expire(key: string, seconds: number): Promise<boolean>;
    ttl(key: string): Promise<number>;
    flushDb(): Promise<string>;
    ping(): Promise<string>;
    isConnected(): boolean;
    getClient(): RedisClientType;
}
export declare const redisService: RedisService;
export default redisService;
//# sourceMappingURL=redis.d.ts.map