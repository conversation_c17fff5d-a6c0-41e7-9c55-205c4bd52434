import { createClient } from 'redis';
import { logger } from '../../utils/logger';
class RedisService {
    client;
    connected = false;
    constructor() {
        this.client = createClient({
            url: process.env.REDIS_URL || 'redis://localhost:6379',
            socket: {
                reconnectStrategy: (retries) => Math.min(retries * 50, 500),
            },
        });
        this.client.on('error', (err) => {
            logger.error('Redis Client Error:', err);
        });
        this.client.on('connect', () => {
            logger.info('Redis Client Connected');
            this.connected = true;
        });
        this.client.on('ready', () => {
            logger.info('Redis Client Ready');
        });
        this.client.on('end', () => {
            logger.info('Redis Client Disconnected');
            this.connected = false;
        });
    }
    async connect() {
        if (!this.connected) {
            await this.client.connect();
        }
    }
    async disconnect() {
        if (this.connected) {
            await this.client.disconnect();
        }
    }
    // Basic operations
    async get(key) {
        return await this.client.get(key);
    }
    async set(key, value, ttl) {
        if (ttl) {
            await this.client.setEx(key, ttl, value);
        }
        else {
            await this.client.set(key, value);
        }
    }
    async del(key) {
        return await this.client.del(key);
    }
    async exists(key) {
        return await this.client.exists(key);
    }
    // JSON operations
    async getJson(key) {
        const value = await this.client.get(key);
        return value ? JSON.parse(value) : null;
    }
    async setJson(key, value, ttl) {
        const jsonValue = JSON.stringify(value);
        await this.set(key, jsonValue, ttl);
    }
    // Hash operations
    async hGet(key, field) {
        return await this.client.hGet(key, field);
    }
    async hSet(key, field, value) {
        return await this.client.hSet(key, field, value);
    }
    async hGetAll(key) {
        return await this.client.hGetAll(key);
    }
    async hDel(key, field) {
        return await this.client.hDel(key, field);
    }
    // List operations
    async lPush(key, value) {
        return await this.client.lPush(key, value);
    }
    async rPush(key, value) {
        return await this.client.rPush(key, value);
    }
    async lPop(key) {
        return await this.client.lPop(key);
    }
    async rPop(key) {
        return await this.client.rPop(key);
    }
    async lRange(key, start, stop) {
        return await this.client.lRange(key, start, stop);
    }
    // Set operations
    async sAdd(key, value) {
        return await this.client.sAdd(key, value);
    }
    async sRem(key, value) {
        return await this.client.sRem(key, value);
    }
    async sMembers(key) {
        return await this.client.sMembers(key);
    }
    async sIsMember(key, value) {
        return await this.client.sIsMember(key, value);
    }
    // Utility methods
    async keys(pattern) {
        return await this.client.keys(pattern);
    }
    async expire(key, seconds) {
        return await this.client.expire(key, seconds);
    }
    async ttl(key) {
        return await this.client.ttl(key);
    }
    async flushDb() {
        return await this.client.flushDb();
    }
    // Health check
    async ping() {
        return await this.client.ping();
    }
    isConnected() {
        return this.connected;
    }
    getClient() {
        return this.client;
    }
}
// Export singleton instance
export const redisService = new RedisService();
export default redisService;
//# sourceMappingURL=redis.js.map