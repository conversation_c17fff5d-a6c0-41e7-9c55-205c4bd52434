import { type AIModel, type TrainingJob } from '../db/schemas/models.js';
export interface ModelDeploymentConfig {
    dockerImage: string;
    environmentVars?: Record<string, string>;
    ports?: Array<{
        containerPort: number;
        hostPort: number;
    }>;
    volumes?: Array<{
        hostPath: string;
        containerPath: string;
    }>;
    resources?: {
        memoryLimit?: string;
        cpuLimit?: string;
    };
}
export interface TrainingConfig {
    epochs: number;
    batchSize: number;
    learningRate?: number;
    datasetPath?: string;
    config?: Record<string, any>;
}
export declare class ModelManagementService {
    getAllModels(): Promise<AIModel[]>;
    getModelById(modelId: string): Promise<AIModel | null>;
    deployModel(modelId: string): Promise<{
        success: boolean;
        containerId?: string;
    }>;
    stopModel(modelId: string): Promise<{
        success: boolean;
    }>;
    getModelMetrics(modelId: string): Promise<any>;
    getTrainingJobs(): Promise<TrainingJob[]>;
    startTraining(modelId: string, config: TrainingConfig): Promise<{
        jobId: string;
    }>;
    getTrainingJob(jobId: string): Promise<TrainingJob | null>;
    private startModelContainer;
    private getDefaultDockerImage;
    private getTrainingDockerImage;
    private startTrainingContainer;
    private monitorTrainingProgress;
    private parseMemoryLimit;
    private parseCpuLimit;
}
export declare const modelManagementService: ModelManagementService;
//# sourceMappingURL=model-management.d.ts.map