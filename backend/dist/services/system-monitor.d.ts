export interface SystemMetrics {
    cpu: {
        usage: number;
        temperature?: number;
        cores: number;
    };
    memory: {
        total: number;
        used: number;
        available: number;
        percentage: number;
    };
    disk: {
        total: number;
        used: number;
        available: number;
        percentage: number;
    };
    network: {
        bytesIn: number;
        bytesOut: number;
        packetsIn: number;
        packetsOut: number;
    };
    system: {
        uptime: number;
        platform: string;
        arch: string;
        hostname: string;
    };
    timestamp: string;
}
export interface ServiceHealth {
    name: string;
    status: 'healthy' | 'unhealthy' | 'unknown';
    url?: string;
    responseTime?: number;
    lastCheck: string;
    error?: string;
}
export declare class SystemMonitorService {
    private static instance;
    private networkStats;
    static getInstance(): SystemMonitorService;
    getSystemMetrics(): Promise<SystemMetrics>;
    checkServiceHealth(services: Array<{
        name: string;
        url: string;
        timeout?: number;
    }>): Promise<ServiceHealth[]>;
    getProcessList(): Promise<Array<{
        pid: number;
        name: string;
        cpu: number;
        memory: number;
        command: string;
    }>>;
    formatBytes(bytes: number): string;
    formatUptime(seconds: number): string;
}
export declare const systemMonitor: SystemMonitorService;
//# sourceMappingURL=system-monitor.d.ts.map