export interface KeyValuePair {
    key: string;
    value: string;
    createRevision: number;
    modRevision: number;
    version: number;
}
export interface ServiceInstance {
    id: string;
    host: string;
    port: number;
    healthy: boolean;
    lastSeen: string;
    metadata?: Record<string, any>;
    ttl?: number;
}
export interface ClusterMember {
    id: string;
    name: string;
    peerURLs: string[];
    clientURLs: string[];
    isLearner: boolean;
}
export interface ClusterInfo {
    clusterId: string;
    members: ClusterMember[];
    leader: string;
    revision: number;
    raftTerm: number;
}
export declare class EtcdService {
    private client;
    private connected;
    private retryCount;
    private maxRetries;
    constructor();
    private init;
    private connect;
    isHealthy(): Promise<{
        status: string;
        connected: boolean;
        timestamp: string;
        version?: string;
    }>;
    getAllKeys(): Promise<KeyValuePair[]>;
    getKey(key: string): Promise<KeyValuePair | null>;
    putKey(key: string, value: string): Promise<boolean>;
    deleteKey(key: string): Promise<boolean>;
    getKeysByPrefix(prefix: string): Promise<KeyValuePair[]>;
    discoverServices(): Promise<{
        serviceName: string;
        instances: ServiceInstance[];
    }[]>;
    private isInstanceHealthy;
    registerService(serviceName: string, serviceUrl: string, metadata?: Record<string, any>, ttl?: number): Promise<string>;
    deregisterService(serviceName: string, serviceId: string): Promise<boolean>;
    getClusterInfo(): Promise<ClusterInfo>;
    watchKey(key: string): Promise<{
        key: string;
        value: string | null;
        watching: boolean;
        watchId: string;
    }>;
}
export declare const etcdService: EtcdService;
//# sourceMappingURL=etcd.d.ts.map