export interface WebSocketMessage {
    type: 'metrics' | 'docker' | 'alert' | 'error' | 'pong';
    data: any;
    timestamp: string;
}
export declare class RealTimeMonitorService {
    private wss;
    private clients;
    private intervals;
    constructor(port?: number);
    private setupWebSocketServer;
    private sendInitialData;
    private handleClientMessage;
    private startMonitoring;
    private sendMetricsUpdate;
    private sendDockerUpdate;
    private sendRandomAlert;
    private sendToClient;
    private broadcast;
    getClientCount(): number;
    stop(): void;
}
export declare const realTimeMonitor: RealTimeMonitorService;
//# sourceMappingURL=realtime-monitor.d.ts.map