import Docker from 'dockerode';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import { promises as fs } from 'fs';
const execAsync = promisify(exec);
export class DockerService {
    docker;
    projectRoot;
    constructor() {
        this.docker = new Docker({
            socketPath: '/var/run/docker.sock'
        });
        this.projectRoot = process.cwd();
    }
    async isDockerRunning() {
        try {
            await this.docker.ping();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async listContainers(all = true) {
        try {
            const containers = await this.docker.listContainers({ all });
            const containerInfos = await Promise.all(containers.map(async (container) => {
                const containerObj = this.docker.getContainer(container.Id);
                // Get container stats if running
                let stats = undefined;
                if (container.State === 'running') {
                    try {
                        const statsStream = await containerObj.stats({ stream: false });
                        stats = this.parseContainerStats(statsStream);
                    }
                    catch (error) {
                        console.error(`Error getting stats for container ${container.Id}:`, error);
                    }
                }
                return {
                    id: container.Id.substring(0, 12),
                    name: container.Names[0].replace(/^\//, ''),
                    image: container.Image,
                    status: this.mapContainerStatus(container.State),
                    state: container.State,
                    ports: container.Ports.map(port => ({
                        privatePort: port.PrivatePort,
                        publicPort: port.PublicPort,
                        type: port.Type
                    })),
                    created: new Date(container.Created * 1000).toISOString(),
                    uptime: container.State === 'running' ? this.calculateUptime(container.Created) : undefined,
                    stats
                };
            }));
            return containerInfos;
        }
        catch (error) {
            console.error('Error listing containers:', error);
            throw new Error('Failed to list containers. Is Docker running?');
        }
    }
    async getContainer(id) {
        try {
            const container = this.docker.getContainer(id);
            const info = await container.inspect();
            let stats = undefined;
            if (info.State.Running) {
                try {
                    const statsStream = await container.stats({ stream: false });
                    stats = this.parseContainerStats(statsStream);
                }
                catch (error) {
                    console.error(`Error getting stats for container ${id}:`, error);
                }
            }
            return {
                id: info.Id.substring(0, 12),
                name: info.Name.replace(/^\//, ''),
                image: info.Config.Image,
                status: this.mapContainerStatus(info.State.Status),
                state: info.State.Status,
                ports: info.NetworkSettings.Ports ? Object.entries(info.NetworkSettings.Ports).flatMap(([port, hostPorts]) => {
                    if (!hostPorts || !Array.isArray(hostPorts))
                        return [];
                    return hostPorts.map(hostPort => ({
                        privatePort: parseInt(port.split('/')[0]),
                        publicPort: hostPort.HostPort ? parseInt(hostPort.HostPort) : undefined,
                        type: port.split('/')[1] || 'tcp'
                    }));
                }) : [],
                created: info.Created,
                uptime: info.State.Running ? this.calculateUptime(new Date(info.State.StartedAt).getTime() / 1000) : undefined,
                stats
            };
        }
        catch (error) {
            console.error(`Error getting container ${id}:`, error);
            return null;
        }
    }
    async startContainer(id) {
        try {
            const container = this.docker.getContainer(id);
            await container.start();
            return true;
        }
        catch (error) {
            console.error(`Error starting container ${id}:`, error);
            return false;
        }
    }
    async stopContainer(id) {
        try {
            const container = this.docker.getContainer(id);
            await container.stop();
            return true;
        }
        catch (error) {
            console.error(`Error stopping container ${id}:`, error);
            return false;
        }
    }
    async restartContainer(id) {
        try {
            const container = this.docker.getContainer(id);
            await container.restart();
            return true;
        }
        catch (error) {
            console.error(`Error restarting container ${id}:`, error);
            return false;
        }
    }
    async getContainerLogs(id, tail = 100) {
        try {
            const container = this.docker.getContainer(id);
            const logs = await container.logs({
                stdout: true,
                stderr: true,
                tail,
                timestamps: true
            });
            return logs.toString();
        }
        catch (error) {
            console.error(`Error getting logs for container ${id}:`, error);
            return 'Error retrieving logs';
        }
    }
    async removeContainer(id) {
        try {
            const container = this.docker.getContainer(id);
            await container.remove({ force: true });
            return true;
        }
        catch (error) {
            console.error(`Error removing container ${id}:`, error);
            return false;
        }
    }
    async createAndStartContainer(config) {
        try {
            const container = await this.docker.createContainer(config);
            await container.start();
            return container.id;
        }
        catch (error) {
            console.error('Error creating and starting container:', error);
            throw new Error(`Failed to create container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Docker Compose Operations
    async executeDockerCompose(command, options = {}) {
        try {
            const dockerComposePath = path.join(this.projectRoot, 'docker-compose.yml');
            // Check if docker-compose.yml exists
            try {
                await fs.access(dockerComposePath);
            }
            catch {
                throw new Error('docker-compose.yml not found in project root');
            }
            let cmd = `docker-compose ${command}`;
            // Add options based on command
            if (command === 'up') {
                if (options.detached)
                    cmd += ' -d';
                if (options.build)
                    cmd += ' --build';
                if (options.recreate)
                    cmd += ' --force-recreate';
                if (options.services)
                    cmd += ` ${options.services.join(' ')}`;
            }
            else if (command === 'down') {
                if (options.removeVolumes)
                    cmd += ' -v';
                if (options.removeImages)
                    cmd += ' --rmi all';
                if (options.removeOrphans)
                    cmd += ' --remove-orphans';
            }
            else if (command === 'logs') {
                if (options.follow)
                    cmd += ' -f';
                if (options.tail)
                    cmd += ` --tail=${options.tail}`;
                if (options.service)
                    cmd += ` ${options.service}`;
            }
            const { stdout, stderr } = await execAsync(cmd, {
                cwd: this.projectRoot,
                timeout: 120000 // 2 minutes timeout
            });
            return stdout || stderr;
        }
        catch (error) {
            console.error(`Docker Compose ${command} failed:`, error);
            throw new Error(`Docker Compose ${command} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getDockerComposeServices() {
        try {
            const output = await this.executeDockerCompose('ps --format json');
            const services = output.split('\n')
                .filter(line => line.trim())
                .map(line => JSON.parse(line));
            return services.map((service) => ({
                name: service.Service,
                image: service.Image,
                status: service.State,
                ports: service.Publishers ? service.Publishers.map((p) => `${p.PublishedPort}:${p.TargetPort}`) : []
            }));
        }
        catch (error) {
            console.error('Failed to get Docker Compose services:', error);
            return [];
        }
    }
    async buildService(serviceName, options = {}) {
        try {
            let cmd = 'build';
            if (options.noCache)
                cmd += ' --no-cache';
            if (serviceName)
                cmd += ` ${serviceName}`;
            return await this.executeDockerCompose(cmd);
        }
        catch (error) {
            throw new Error(`Failed to build service: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async pullImages(serviceName) {
        try {
            let cmd = 'pull';
            if (serviceName)
                cmd += ` ${serviceName}`;
            return await this.executeDockerCompose(cmd);
        }
        catch (error) {
            throw new Error(`Failed to pull images: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getComposeStatus() {
        try {
            const services = await this.getDockerComposeServices();
            const runningServices = services.filter(s => s.status === 'running').length;
            // Get detailed container info for compose services
            const containers = await this.listContainers(true);
            const composeContainers = containers.filter(c => c.name.includes('wow-') || // Based on container naming in docker-compose.yml
                services.some(s => c.name.includes(s.name)));
            return {
                isRunning: runningServices > 0,
                services: services.length,
                runningServices,
                containers: composeContainers.map(c => ({
                    name: c.name,
                    service: this.extractServiceName(c.name),
                    status: c.status,
                    health: this.getContainerHealth(c)
                }))
            };
        }
        catch (error) {
            return {
                isRunning: false,
                services: 0,
                runningServices: 0,
                containers: []
            };
        }
    }
    // Image Management
    async getImages() {
        try {
            const images = await this.docker.listImages();
            return images.map(image => ({
                id: image.Id.substring(0, 12),
                repository: image.RepoTags?.[0]?.split(':')[0] || '<none>',
                tag: image.RepoTags?.[0]?.split(':')[1] || '<none>',
                size: image.Size,
                created: new Date(image.Created * 1000).toISOString()
            }));
        }
        catch (error) {
            console.error('Failed to list images:', error);
            return [];
        }
    }
    async removeImage(imageId, force = false) {
        try {
            const image = this.docker.getImage(imageId);
            await image.remove({ force });
            return true;
        }
        catch (error) {
            console.error(`Failed to remove image ${imageId}:`, error);
            return false;
        }
    }
    // Network Management
    async getNetworks() {
        try {
            const networks = await this.docker.listNetworks();
            return networks.map(network => ({
                id: network.Id.substring(0, 12),
                name: network.Name,
                driver: network.Driver,
                scope: network.Scope,
                created: network.Created,
                containers: network.Containers ? Object.keys(network.Containers).length : 0
            }));
        }
        catch (error) {
            console.error('Failed to list networks:', error);
            return [];
        }
    }
    // Volume Management
    async getVolumes() {
        try {
            const { Volumes } = await this.docker.listVolumes();
            return (Volumes || []).map(volume => ({
                name: volume.Name,
                driver: volume.Driver,
                mountpoint: volume.Mountpoint,
                created: volume.CreatedAt || new Date().toISOString(),
                size: volume.UsageData?.Size
            }));
        }
        catch (error) {
            console.error('Failed to list volumes:', error);
            return [];
        }
    }
    async removeVolume(volumeName, force = false) {
        try {
            const volume = this.docker.getVolume(volumeName);
            await volume.remove({ force });
            return true;
        }
        catch (error) {
            console.error(`Failed to remove volume ${volumeName}:`, error);
            return false;
        }
    }
    // System Information
    async getSystemDf() {
        try {
            const df = await this.docker.df();
            return {
                containers: {
                    active: df.Containers?.filter((c) => c.State === 'running').length || 0,
                    total: df.Containers?.length || 0,
                    size: df.Containers?.reduce((sum, c) => sum + (c.SizeRw || 0), 0) || 0,
                    reclaimable: df.Containers?.reduce((sum, c) => sum + (c.SizeRootFs || 0), 0) || 0
                },
                images: {
                    active: df.Images?.filter((i) => i.Containers > 0).length || 0,
                    total: df.Images?.length || 0,
                    size: df.Images?.reduce((sum, i) => sum + (i.Size || 0), 0) || 0,
                    reclaimable: df.Images?.reduce((sum, i) => sum + (i.SharedSize || 0), 0) || 0
                },
                volumes: {
                    active: df.Volumes?.filter((v) => v.UsageData?.RefCount > 0).length || 0,
                    total: df.Volumes?.length || 0,
                    size: df.Volumes?.reduce((sum, v) => sum + (v.UsageData?.Size || 0), 0) || 0,
                    reclaimable: 0 // Docker doesn't provide this for volumes
                }
            };
        }
        catch (error) {
            console.error('Failed to get system df:', error);
            return {
                containers: { active: 0, total: 0, size: 0, reclaimable: 0 },
                images: { active: 0, total: 0, size: 0, reclaimable: 0 },
                volumes: { active: 0, total: 0, size: 0, reclaimable: 0 }
            };
        }
    }
    // Private helper methods
    mapContainerStatus(state) {
        switch (state.toLowerCase()) {
            case 'running':
                return 'running';
            case 'exited':
                return 'stopped';
            case 'created':
                return 'stopped';
            case 'restarting':
                return 'restarting';
            case 'dead':
                return 'error';
            default:
                return 'stopped';
        }
    }
    calculateUptime(createdTimestamp) {
        const now = Date.now();
        const created = createdTimestamp * 1000;
        const uptimeMs = now - created;
        const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
        const hours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        }
        else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        }
        else {
            return `${minutes}m`;
        }
    }
    parseContainerStats(stats) {
        if (!stats)
            return undefined;
        try {
            // Calculate CPU usage percentage
            const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - (stats.precpu_stats.cpu_usage?.total_usage || 0);
            const systemDelta = stats.cpu_stats.system_cpu_usage - (stats.precpu_stats.system_cpu_usage || 0);
            const cpuUsage = systemDelta > 0 ? (cpuDelta / systemDelta) * stats.cpu_stats.online_cpus * 100 : 0;
            // Calculate memory usage
            const memoryUsage = stats.memory_stats.usage || 0;
            const memoryLimit = stats.memory_stats.limit || 0;
            // Calculate network usage
            const networks = stats.networks || {};
            let networkRx = 0;
            let networkTx = 0;
            Object.values(networks).forEach((network) => {
                networkRx += network.rx_bytes || 0;
                networkTx += network.tx_bytes || 0;
            });
            return {
                cpuUsage: Math.round(cpuUsage * 100) / 100,
                memoryUsage,
                memoryLimit,
                networkRx,
                networkTx
            };
        }
        catch (error) {
            console.error('Error parsing container stats:', error);
            return undefined;
        }
    }
    extractServiceName(containerName) {
        // Extract service name from container name (e.g., wow-postgres -> postgres)
        const parts = containerName.split('-');
        return parts.length > 1 ? parts.slice(1).join('-') : containerName;
    }
    getContainerHealth(container) {
        if (container.status === 'running') {
            return 'healthy';
        }
        else if (container.status === 'restarting') {
            return 'unhealthy';
        }
        return 'stopped';
    }
}
export const dockerService = new DockerService();
//# sourceMappingURL=docker-enhanced.js.map