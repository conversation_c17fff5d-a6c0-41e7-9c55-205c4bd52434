import { MilvusClient, DataType, MetricType } from '@zilliz/milvus2-sdk-node';
import { logger } from '../../utils/logger';
class MilvusService {
    client;
    connected = false;
    constructor() {
        this.client = new MilvusClient({
            address: process.env.MILVUS_URL || 'http://localhost:19530',
        });
    }
    async connect() {
        try {
            const response = await this.client.checkHealth();
            if (!!response.isHealthy) {
                this.connected = true;
                logger.info('Milvus Client Connected');
            }
            else {
                throw new Error('Mil<PERSON><PERSON> is not healthy');
            }
        }
        catch (error) {
            logger.error('Failed to connect to Milvu<PERSON>:', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.connected) {
            // Milvus client doesn't have explicit disconnect method
            this.connected = false;
            logger.info('Milvus Client Disconnected');
        }
    }
    // Collection management
    async createCollection(collectionName, dimension, description) {
        const params = {
            collection_name: collectionName,
            description: description || `Collection for ${collectionName}`,
            fields: [
                {
                    name: 'id',
                    data_type: DataType.VarChar,
                    max_length: 100,
                    is_primary_key: true,
                },
                {
                    name: 'vector',
                    data_type: DataType.FloatVector,
                    dim: dimension,
                },
                {
                    name: 'metadata',
                    data_type: DataType.JSON,
                },
            ],
        };
        const response = await this.client.createCollection(params);
        if (response.error_code !== 'Success') {
            throw new Error(`Failed to create collection: ${response.reason}`);
        }
        logger.info(`Collection ${collectionName} created successfully`);
    }
    async hasCollection(collectionName) {
        const response = await this.client.hasCollection({
            collection_name: collectionName,
        });
        return !!response.value;
    }
    async dropCollection(collectionName) {
        const response = await this.client.dropCollection({
            collection_name: collectionName,
        });
        if (response.error_code !== 'Success') {
            throw new Error(`Failed to drop collection: ${response.reason}`);
        }
        logger.info(`Collection ${collectionName} dropped successfully`);
    }
    async listCollections() {
        const response = await this.client.listCollections();
        return response.data?.map((collection) => collection.name) || [];
    }
    // Index management
    async createIndex(collectionName, fieldName = 'vector', indexType = 'IVF_FLAT', metricType = MetricType.L2, params) {
        const indexParams = {
            collection_name: collectionName,
            field_name: fieldName,
            index_type: indexType,
            metric_type: metricType,
            params: params || { nlist: 1024 },
        };
        const response = await this.client.createIndex(indexParams);
        if (response.error_code !== 'Success') {
            throw new Error(`Failed to create index: ${response.reason}`);
        }
        logger.info(`Index created for collection ${collectionName}`);
    }
    // Collection operations
    async loadCollection(collectionName) {
        const response = await this.client.loadCollection({
            collection_name: collectionName,
        });
        if (response.error_code !== 'Success') {
            throw new Error(`Failed to load collection: ${response.reason}`);
        }
    }
    async releaseCollection(collectionName) {
        const response = await this.client.releaseCollection({
            collection_name: collectionName,
        });
        if (response.error_code !== 'Success') {
            throw new Error(`Failed to release collection: ${response.reason}`);
        }
    }
    // Data operations
    async insert(collectionName, documents) {
        const data = documents.map(doc => ({
            id: doc.id,
            vector: doc.vector,
            metadata: doc.metadata || {},
        }));
        const response = await this.client.insert({
            collection_name: collectionName,
            data,
        });
        if (response.status.error_code !== 'Success') {
            throw new Error(`Failed to insert data: ${response.status.reason}`);
        }
        logger.info(`Inserted ${documents.length} documents into ${collectionName}`);
    }
    async search(collectionName, queryVectors, topK = 10, params) {
        const searchParams = {
            collection_name: collectionName,
            vectors: queryVectors,
            search_params: {
                anns_field: 'vector',
                topk: topK,
                metric_type: MetricType.L2,
                params: params || { nprobe: 10 },
            },
            output_fields: ['id', 'metadata'],
        };
        const response = await this.client.search(searchParams);
        if (response.status.error_code !== 'Success') {
            throw new Error(`Search failed: ${response.status.reason}`);
        }
        return response.results.map((result) => result.map((item) => ({
            id: item.id,
            score: item.score,
            metadata: item.metadata,
        })));
    }
    async delete(collectionName, ids) {
        const response = await this.client.delete({
            collection_name: collectionName,
            ids,
        });
        if (response.status.error_code !== 'Success') {
            throw new Error(`Failed to delete documents: ${response.status.reason}`);
        }
        logger.info(`Deleted ${ids.length} documents from ${collectionName}`);
    }
    async query(collectionName, expr, outputFields) {
        const response = await this.client.query({
            collection_name: collectionName,
            expr,
            output_fields: outputFields || ['*'],
        });
        if (response.status.error_code !== 'Success') {
            throw new Error(`Query failed: ${response.status.reason}`);
        }
        return response.data;
    }
    // Utility methods
    async getCollectionStatistics(collectionName) {
        const response = await this.client.getCollectionStatistics({
            collection_name: collectionName,
        });
        if (response.status.error_code !== 'Success') {
            throw new Error(`Failed to get statistics: ${response.status.reason}`);
        }
        return response.stats;
    }
    async flush(collectionName) {
        const response = await this.client.flush({
            collection_names: [collectionName],
        });
        if (response.status.error_code !== 'Success') {
            throw new Error(`Failed to flush collection: ${response.status.reason}`);
        }
    }
    // Health check
    async checkHealth() {
        try {
            const response = await this.client.checkHealth();
            return response.isHealthy;
        }
        catch (error) {
            logger.error('Milvus health check failed:', error);
            return false;
        }
    }
    isConnected() {
        return this.connected;
    }
    getClient() {
        return this.client;
    }
}
// Export singleton instance
export const milvusService = new MilvusService();
export default milvusService;
//# sourceMappingURL=milvus.js.map