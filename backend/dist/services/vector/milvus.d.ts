import { MilvusClient, MetricType } from '@zilliz/milvus2-sdk-node';
export interface VectorSearchResult {
    id: string;
    score: number;
    metadata?: Record<string, any>;
}
export interface VectorDocument {
    id: string;
    vector: number[];
    metadata?: Record<string, any>;
}
declare class MilvusService {
    private client;
    private connected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    createCollection(collectionName: string, dimension: number, description?: string): Promise<void>;
    hasCollection(collectionName: string): Promise<boolean>;
    dropCollection(collectionName: string): Promise<void>;
    listCollections(): Promise<string[]>;
    createIndex(collectionName: string, fieldName?: string, indexType?: string, metricType?: MetricType, params?: Record<string, any>): Promise<void>;
    loadCollection(collectionName: string): Promise<void>;
    releaseCollection(collectionName: string): Promise<void>;
    insert(collectionName: string, documents: VectorDocument[]): Promise<void>;
    search(collectionName: string, queryVectors: number[][], topK?: number, params?: Record<string, any>): Promise<VectorSearchResult[][]>;
    delete(collectionName: string, ids: string[]): Promise<void>;
    query(collectionName: string, expr: string, outputFields?: string[]): Promise<any[]>;
    getCollectionStatistics(collectionName: string): Promise<any>;
    flush(collectionName: string): Promise<void>;
    checkHealth(): Promise<boolean>;
    isConnected(): boolean;
    getClient(): MilvusClient;
}
export declare const milvusService: MilvusService;
export default milvusService;
//# sourceMappingURL=milvus.d.ts.map