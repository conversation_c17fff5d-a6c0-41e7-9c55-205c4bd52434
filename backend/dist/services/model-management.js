import { db } from '../db/index.js';
import { aiModels, trainingJobs, modelMetrics } from '../db/schemas/models.js';
import { dockerService } from './docker.js';
import { logger } from '../utils/logger.js';
import { eq, desc } from 'drizzle-orm';
export class ModelManagementService {
    async getAllModels() {
        try {
            const models = await db
                .select()
                .from(aiModels)
                .orderBy(desc(aiModels.updatedAt));
            return models;
        }
        catch (error) {
            logger.error('Failed to get models from database:', error);
            throw new Error('Failed to retrieve models');
        }
    }
    async getModelById(modelId) {
        try {
            const model = await db
                .select()
                .from(aiModels)
                .where(eq(aiModels.id, modelId))
                .limit(1);
            return model[0] || null;
        }
        catch (error) {
            logger.error('Failed to get model by ID:', error);
            throw new Error('Failed to retrieve model');
        }
    }
    async deployModel(modelId) {
        try {
            const model = await this.getModelById(modelId);
            if (!model) {
                throw new Error('Model not found');
            }
            // Check if model is already running
            if (model.status === 'running' && model.containerId) {
                const containers = await dockerService.listContainers();
                const existingContainer = containers.find(c => c.id === model.containerId);
                if (existingContainer && existingContainer.status === 'running') {
                    return { success: true, containerId: model.containerId };
                }
            }
            // Deploy model using Docker
            const deploymentConfig = {
                dockerImage: model.dockerImage || this.getDefaultDockerImage(model.type),
                environmentVars: {
                    MODEL_PATH: model.modelPath || `/models/${model.name}`,
                    MODEL_TYPE: model.type,
                    MODEL_VERSION: model.version,
                },
                ports: [{ containerPort: 8000, hostPort: 0 }], // Use dynamic port
                volumes: model.modelPath ? [{
                        hostPath: model.modelPath,
                        containerPath: `/models/${model.name}`
                    }] : [],
            };
            const containerId = await this.startModelContainer(model.name, deploymentConfig);
            // Update model status in database
            await db
                .update(aiModels)
                .set({
                status: 'running',
                containerId,
                updatedAt: new Date(),
            })
                .where(eq(aiModels.id, modelId));
            logger.info(`Model ${modelId} deployed successfully with container ${containerId}`);
            return { success: true, containerId };
        }
        catch (error) {
            logger.error('Failed to deploy model:', error);
            // Update model status to error
            await db
                .update(aiModels)
                .set({
                status: 'error',
                updatedAt: new Date(),
            })
                .where(eq(aiModels.id, modelId));
            throw new Error(`Failed to deploy model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async stopModel(modelId) {
        try {
            const model = await this.getModelById(modelId);
            if (!model) {
                throw new Error('Model not found');
            }
            if (model.containerId) {
                await dockerService.stopContainer(model.containerId);
                await dockerService.removeContainer(model.containerId);
            }
            // Update model status in database
            await db
                .update(aiModels)
                .set({
                status: 'idle',
                containerId: null,
                updatedAt: new Date(),
            })
                .where(eq(aiModels.id, modelId));
            logger.info(`Model ${modelId} stopped successfully`);
            return { success: true };
        }
        catch (error) {
            logger.error('Failed to stop model:', error);
            throw new Error(`Failed to stop model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getModelMetrics(modelId) {
        try {
            // Get latest metrics from database
            const latestMetrics = await db
                .select()
                .from(modelMetrics)
                .where(eq(modelMetrics.modelId, modelId))
                .orderBy(desc(modelMetrics.timestamp))
                .limit(1);
            if (latestMetrics.length === 0) {
                // Return default metrics if none exist
                return {
                    accuracy: 0.0,
                    precision: 0.0,
                    recall: 0.0,
                    f1Score: 0.0,
                    latency: 0,
                    throughput: 0,
                    memoryUsage: 0.0,
                    cpuUsage: 0.0,
                };
            }
            const metrics = latestMetrics[0];
            return {
                accuracy: parseFloat(metrics.accuracy || '0'),
                precision: parseFloat(metrics.precision || '0'),
                recall: parseFloat(metrics.recall || '0'),
                f1Score: parseFloat(metrics.f1Score || '0'),
                latency: metrics.latency,
                throughput: metrics.throughput,
                memoryUsage: parseFloat(metrics.memoryUsage || '0'),
                cpuUsage: parseFloat(metrics.cpuUsage || '0'),
            };
        }
        catch (error) {
            logger.error('Failed to get model metrics:', error);
            throw new Error('Failed to retrieve model metrics');
        }
    }
    async getTrainingJobs() {
        try {
            const jobs = await db
                .select()
                .from(trainingJobs)
                .orderBy(desc(trainingJobs.createdAt));
            return jobs;
        }
        catch (error) {
            logger.error('Failed to get training jobs:', error);
            throw new Error('Failed to retrieve training jobs');
        }
    }
    async startTraining(modelId, config) {
        try {
            const model = await this.getModelById(modelId);
            if (!model) {
                throw new Error('Model not found');
            }
            // Create training job record
            const newJob = {
                modelId,
                status: 'pending',
                progress: 0,
                startTime: new Date(),
                epochs: config.epochs,
                currentEpoch: 0,
                batchSize: config.batchSize,
                learningRate: config.learningRate?.toString(),
                datasetPath: config.datasetPath,
                config: config.config ? JSON.stringify(config.config) : null,
            };
            const [job] = await db
                .insert(trainingJobs)
                .values(newJob)
                .returning();
            // TODO: Start actual training process
            // This would typically involve:
            // 1. Starting a training container
            // 2. Monitoring training progress
            // 3. Updating job status and metrics
            // Start training container with the model configuration
            try {
                const trainingImage = this.getTrainingDockerImage(model.type);
                const trainingContainerId = await this.startTrainingContainer(job.id, {
                    dockerImage: trainingImage,
                    environmentVars: {
                        MODEL_ID: modelId,
                        JOB_ID: job.id,
                        DATASET_PATH: config.datasetPath || '/data',
                        EPOCHS: config.epochs.toString(),
                        BATCH_SIZE: config.batchSize.toString(),
                        LEARNING_RATE: config.learningRate?.toString() || '0.001',
                        MODEL_PATH: model.modelPath || `/models/${model.name}`,
                    },
                    volumes: [
                        { hostPath: config.datasetPath || '/tmp/data', containerPath: '/data' },
                        { hostPath: model.modelPath || `/models/${model.name}`, containerPath: '/model' },
                    ],
                });
                // Update job with container ID - remove this since containerId is not in schema
                // await db
                //   .update(trainingJobs)
                //   .set({ 
                //     status: 'running',
                //     containerId: trainingContainerId,
                //     updatedAt: new Date(),
                //   })
                //   .where(eq(trainingJobs.id, job.id));
                // Update job status to running
                await db
                    .update(trainingJobs)
                    .set({
                    status: 'running',
                    updatedAt: new Date(),
                })
                    .where(eq(trainingJobs.id, job.id));
                // Start monitoring the training progress
                this.monitorTrainingProgress(job.id);
            }
            catch (trainingError) {
                logger.error('Failed to start training container:', trainingError);
                // Update job status to failed
                await db
                    .update(trainingJobs)
                    .set({
                    status: 'failed',
                    logs: `Training failed to start: ${trainingError instanceof Error ? trainingError.message : 'Unknown error'}`,
                    updatedAt: new Date(),
                })
                    .where(eq(trainingJobs.id, job.id));
                throw new Error(`Failed to start training: ${trainingError instanceof Error ? trainingError.message : 'Unknown error'}`);
            }
            logger.info(`Training job ${job.id} started for model ${modelId}`);
            return { jobId: job.id };
        }
        catch (error) {
            logger.error('Failed to start training:', error);
            throw new Error(`Failed to start training: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getTrainingJob(jobId) {
        try {
            const job = await db
                .select()
                .from(trainingJobs)
                .where(eq(trainingJobs.id, jobId))
                .limit(1);
            return job[0] || null;
        }
        catch (error) {
            logger.error('Failed to get training job:', error);
            throw new Error('Failed to retrieve training job');
        }
    }
    async startModelContainer(modelName, config) {
        try {
            // Create container configuration
            const containerConfig = {
                Image: config.dockerImage,
                name: `model-${modelName}-${Date.now()}`,
                Env: Object.entries(config.environmentVars || {}).map(([key, value]) => `${key}=${value}`),
                ExposedPorts: {},
                HostConfig: {
                    PortBindings: {},
                    Binds: config.volumes?.map(v => `${v.hostPath}:${v.containerPath}`) || [],
                    Memory: config.resources?.memoryLimit ? this.parseMemoryLimit(config.resources.memoryLimit) : undefined,
                    CpuQuota: config.resources?.cpuLimit ? this.parseCpuLimit(config.resources.cpuLimit) : undefined,
                },
            };
            // Configure ports
            if (config.ports) {
                for (const port of config.ports) {
                    const portKey = `${port.containerPort}/tcp`;
                    containerConfig.ExposedPorts[portKey] = {};
                    containerConfig.HostConfig.PortBindings[portKey] = [{ HostPort: port.hostPort.toString() }];
                }
            }
            // Start container using Docker service
            const containerId = await dockerService.createAndStartContainer(containerConfig);
            return containerId;
        }
        catch (error) {
            logger.error('Failed to start model container:', error);
            throw error;
        }
    }
    getDefaultDockerImage(modelType) {
        const defaultImages = {
            'embedding': 'tensorflow/serving:latest',
            'llm': 'pytorch/pytorch:latest',
            'classification': 'tensorflow/tensorflow:latest',
            'generation': 'pytorch/pytorch:latest',
            'other': 'python:3.9-slim',
        };
        return defaultImages[modelType] || defaultImages.other;
    }
    getTrainingDockerImage(modelType) {
        const imageMap = {
            'embedding': 'pytorch/pytorch:latest',
            'llm': 'huggingface/transformers-pytorch-gpu:latest',
            'classification': 'tensorflow/tensorflow:latest-gpu',
            'generation': 'nvidia/pytorch:22.08-py3',
            'other': 'python:3.9-slim'
        };
        return imageMap[modelType] || imageMap.other;
    }
    async startTrainingContainer(jobId, config) {
        try {
            const containerName = `training-${jobId}`;
            const containerId = await dockerService.createAndStartContainer({
                name: containerName,
                Image: config.dockerImage,
                Env: Object.entries(config.environmentVars || {}).map(([key, value]) => `${key}=${value}`),
                HostConfig: {
                    Binds: config.volumes?.map(vol => `${vol.hostPath}:${vol.containerPath}`) || [],
                    PortBindings: {},
                    AutoRemove: false,
                    RestartPolicy: { Name: 'no' },
                },
            });
            return containerId;
        }
        catch (error) {
            logger.error('Failed to start training container:', error);
            throw error;
        }
    }
    async monitorTrainingProgress(jobId) {
        // This would typically monitor logs and update progress in a background process
        // For now, we'll just log that monitoring has started
        logger.info(`Started monitoring training progress for job ${jobId}`);
        // In a real implementation, you would:
        // 1. Set up log streaming from the training container
        // 2. Parse training metrics from logs
        // 3. Update the training job progress and metrics in the database
        // 4. Handle completion/failure states
    }
    parseMemoryLimit(limit) {
        const units = { 'b': 1, 'k': 1024, 'm': 1024 * 1024, 'g': 1024 * 1024 * 1024 };
        const match = limit.toLowerCase().match(/^(\d+)([bkmg]?)$/);
        if (match) {
            const value = parseInt(match[1]);
            const unit = match[2] || 'b';
            return value * (units[unit] || 1);
        }
        return 0;
    }
    parseCpuLimit(limit) {
        // Convert CPU limit (e.g., "0.5", "1.0") to Docker CPU quota
        const cpuValue = parseFloat(limit);
        return Math.round(cpuValue * 100000); // Docker uses microseconds
    }
}
export const modelManagementService = new ModelManagementService();
//# sourceMappingURL=model-management.js.map