import { initTRPC, TRPCError } from '@trpc/server';
import superjson from 'superjson';
import * as v from 'valibot';
import { db } from '../db';
import { validateRequest } from '../auth';
// Create context for tRPC
export async function createContext({ req, res }) {
    // Validate session from request
    const { user, session } = await validateRequest(req);
    return {
        db,
        user,
        session,
        req,
        res,
    };
}
// Initialize tRPC
const t = initTRPC.context().create({
    transformer: superjson,
    errorFormatter({ shape, error }) {
        return {
            ...shape,
            data: {
                ...shape.data,
                zodError: error.cause instanceof Error ? error.cause.message : null,
            },
        };
    },
});
// Export reusable router and procedure helpers
export const router = t.router;
export const publicProcedure = t.procedure;
// Custom input function for Valibot
export function input(schema) {
    return {
        input: (rawInput) => {
            try {
                return v.parse(schema, rawInput);
            }
            catch (error) {
                throw new TRPCError({
                    code: 'BAD_REQUEST',
                    message: 'Invalid input',
                    cause: error,
                });
            }
        },
    };
}
// Protected procedure middleware
const isAuthenticated = t.middleware(({ next, ctx }) => {
    if (!ctx.user || !ctx.session) {
        throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'You must be logged in to access this resource',
        });
    }
    return next({
        ctx: {
            ...ctx,
            user: ctx.user,
            session: ctx.session,
        },
    });
});
// Admin-only procedure middleware  
const isAdmin = t.middleware(({ next, ctx }) => {
    if (!ctx.user || ctx.user.role !== 'admin') {
        throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Admin access required',
        });
    }
    return next({
        ctx: {
            ...ctx,
            user: ctx.user,
        },
    });
});
export const protectedProcedure = publicProcedure.use(isAuthenticated);
export const adminProcedure = protectedProcedure.use(isAdmin);
//# sourceMappingURL=trpc.js.map