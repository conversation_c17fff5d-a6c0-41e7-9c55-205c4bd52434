import { Client } from 'minio';
import { logger } from '../utils/logger.js';
// MinIO client configuration
export const minioClient = new Client({
    endPoint: process.env.MINIO_ENDPOINT || 'localhost',
    port: parseInt(process.env.MINIO_PORT || '9000'),
    useSSL: process.env.MINIO_USE_SSL === 'true',
    accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
});
// Default bucket name
export const DEFAULT_BUCKET = process.env.MINIO_DEFAULT_BUCKET || process.env.MINIO_BUCKET || 'file-storage';
// Initialize MinIO - ensure bucket exists
export async function initializeMinIO() {
    try {
        // Check if bucket exists, create if not
        const bucketExists = await minioClient.bucketExists(DEFAULT_BUCKET);
        if (!bucketExists) {
            await minioClient.makeBucket(DEFAULT_BUCKET);
            logger.info(`Created MinIO bucket: ${DEFAULT_BUCKET}`);
        }
        // Set bucket policy for public read access (adjust as needed)
        const policy = {
            Version: '2012-10-17',
            Statement: [
                {
                    Effect: 'Allow',
                    Principal: { AWS: ['*'] },
                    Action: ['s3:GetObject'],
                    Resource: [`arn:aws:s3:::${DEFAULT_BUCKET}/*`],
                },
            ],
        };
        await minioClient.setBucketPolicy(DEFAULT_BUCKET, JSON.stringify(policy));
        logger.info('MinIO initialized successfully');
    }
    catch (error) {
        logger.error('Failed to initialize MinIO:', error);
        throw error;
    }
}
// Helper function to get file URL
export function getFileUrl(objectName) {
    const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
    const port = process.env.MINIO_PORT || '9000';
    const useSSL = process.env.MINIO_USE_SSL === 'true';
    const protocol = useSSL ? 'https' : 'http';
    const portSuffix = (useSSL && port === '443') || (!useSSL && port === '80') ? '' : `:${port}`;
    return `${protocol}://${endpoint}${portSuffix}/${DEFAULT_BUCKET}/${objectName}`;
}
//# sourceMappingURL=minio.js.map