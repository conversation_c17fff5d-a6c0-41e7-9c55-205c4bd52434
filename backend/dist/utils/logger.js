import pino from 'pino';
const isProduction = process.env.NODE_ENV === 'production';
const logLevel = process.env.LOG_LEVEL || (isProduction ? 'info' : 'debug');
export const logger = pino({
    level: logLevel,
    transport: !isProduction
        ? {
            target: 'pino-pretty',
            options: {
                colorize: true,
                translateTime: 'SYS:standard',
                ignore: 'pid,hostname',
            },
        }
        : undefined,
    formatters: {
        level: (label) => {
            return { level: label };
        },
    },
    timestamp: pino.stdTimeFunctions.isoTime,
    redact: {
        paths: [
            'password',
            'token',
            'accessToken',
            'refreshToken',
            'secret',
            'authorization',
            'cookie',
        ],
        censor: '[REDACTED]',
    },
});
export default logger;
//# sourceMappingURL=logger.js.map