export interface LogEntry {
    id: string;
    timestamp: string;
    level: 'info' | 'warn' | 'error' | 'debug';
    service: string;
    message: string;
    data?: any;
}
export declare const logsRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    list: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            search?: string | undefined;
            limit?: number | undefined;
            offset?: number | undefined;
            level?: "error" | "debug" | "info" | "warn" | undefined;
            service?: string | undefined;
        };
        output: {
            logs: LogEntry[];
            total: number;
            hasMore: boolean;
        };
    }>;
    stats: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            total: number;
            last24Hours: number;
            lastHour: number;
            errors: number;
            warnings: number;
            services: string[];
            errorRate: number;
            levels: {
                info: number;
                warn: number;
                error: number;
                debug: number;
            };
        };
    }>;
    services: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: string[];
    }>;
    clear: import("@trpc/server").TRPCMutationProcedure<{
        input: void;
        output: {
            success: boolean;
            clearedCount: number;
        };
    }>;
}>;
//# sourceMappingURL=logs.d.ts.map