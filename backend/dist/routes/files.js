import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { minioClient, DEFAULT_BUCKET, getFileUrl } from '../lib/minio.js';
import { logger } from '../utils/logger.js';
// Schemas
const UploadFileSchema = z.object({
    fileName: z.string().min(1),
    fileContent: z.string(), // Base64 encoded
    contentType: z.string().optional(),
    path: z.string().default(''),
});
const DeleteFileSchema = z.object({
    objectNames: z.array(z.string()),
});
const GetFileSchema = z.object({
    objectName: z.string(),
});
const ListFilesSchema = z.object({
    path: z.string().default(''),
    recursive: z.boolean().default(false),
}).optional();
const CreateFolderSchema = z.object({
    folderPath: z.string().min(1),
});
// Helper function to convert stream to buffer
async function streamToBuffer(stream) {
    const chunks = [];
    return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
    });
}
// Helper function to format file size
function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
export const filesRouter = router({
    // List files and folders
    list: publicProcedure
        .input(ListFilesSchema)
        .query(async ({ input }) => {
        try {
            const { path = '', recursive = false } = input || {};
            const prefix = path ? `${path.replace(/^\/+|\/+$/g, '')}/` : '';
            const objectsStream = minioClient.listObjects(DEFAULT_BUCKET, prefix, recursive);
            const files = [];
            const folders = new Set();
            return new Promise((resolve, reject) => {
                objectsStream.on('data', (obj) => {
                    if (obj.name) {
                        const relativePath = obj.name.replace(prefix, '');
                        if (!recursive && relativePath.includes('/')) {
                            // This is a file in a subfolder, extract folder name
                            const folderName = relativePath.split('/')[0];
                            if (folderName) {
                                folders.add(folderName);
                            }
                        }
                        else if (obj.name.endsWith('/')) {
                            // This is a folder
                            const folderName = relativePath.replace('/', '');
                            if (folderName) {
                                folders.add(folderName);
                            }
                        }
                        else {
                            // This is a file
                            files.push({
                                id: obj.name,
                                name: relativePath || obj.name.split('/').pop(),
                                type: 'file',
                                size: obj.size,
                                lastModified: obj.lastModified?.toISOString() || new Date().toISOString(),
                                mimeType: getMimeType(obj.name || ''),
                                url: getFileUrl(obj.name),
                                etag: obj.etag,
                            });
                        }
                    }
                });
                objectsStream.on('end', () => {
                    // Add folders to the result
                    const folderItems = Array.from(folders).map(folderName => ({
                        id: `${prefix}${folderName}/`,
                        name: folderName,
                        type: 'folder',
                        lastModified: new Date().toISOString(),
                    }));
                    resolve([...folderItems, ...files]);
                });
                objectsStream.on('error', reject);
            });
        }
        catch (error) {
            logger.error('Error listing files:', error);
            throw new Error('Failed to list files');
        }
    }),
    // Get storage statistics
    stats: publicProcedure
        .query(async () => {
        try {
            const objectsStream = minioClient.listObjects(DEFAULT_BUCKET, '', true);
            let totalSize = 0;
            let fileCount = 0;
            let folderCount = 0;
            return new Promise((resolve, reject) => {
                objectsStream.on('data', (obj) => {
                    if (obj.name?.endsWith('/')) {
                        folderCount++;
                    }
                    else {
                        fileCount++;
                        totalSize += obj.size || 0;
                    }
                });
                objectsStream.on('end', () => {
                    resolve({
                        used: totalSize,
                        total: 10 * 1024 * 1024 * 1024, // 10GB limit (configurable)
                        files: fileCount,
                        folders: folderCount,
                        formattedSize: formatFileSize(totalSize),
                    });
                });
                objectsStream.on('error', reject);
            });
        }
        catch (error) {
            logger.error('Error getting storage stats:', error);
            throw new Error('Failed to get storage statistics');
        }
    }),
    // Upload file
    upload: publicProcedure
        .input(UploadFileSchema)
        .mutation(async ({ input }) => {
        try {
            const { fileName, fileContent, contentType, path } = input;
            const objectName = path ? `${path.replace(/^\/+|\/+$/g, '')}/${fileName}` : fileName;
            logger.info(`Starting upload for file: ${fileName}, size: ${fileContent.length} chars`);
            // Validate base64 content
            if (!fileContent || typeof fileContent !== 'string') {
                throw new Error('Invalid file content - must be base64 string');
            }
            // Decode base64 content
            let buffer;
            try {
                buffer = Buffer.from(fileContent, 'base64');
                logger.info(`Decoded buffer size: ${buffer.length} bytes`);
            }
            catch (decodeError) {
                logger.error('Failed to decode base64:', decodeError);
                throw new Error('Invalid base64 content');
            }
            // Upload to MinIO
            const uploadInfo = await minioClient.putObject(DEFAULT_BUCKET, objectName, buffer, buffer.length, {
                'Content-Type': contentType || getMimeType(fileName),
            });
            logger.info(`File uploaded successfully: ${objectName}`, uploadInfo);
            return {
                id: objectName,
                name: fileName,
                type: 'file',
                size: buffer.length,
                lastModified: new Date().toISOString(),
                mimeType: contentType || getMimeType(fileName),
                url: getFileUrl(objectName),
                etag: uploadInfo.etag,
            };
        }
        catch (error) {
            logger.error('Error uploading file:', error);
            throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Delete files
    delete: publicProcedure
        .input(DeleteFileSchema)
        .mutation(async ({ input }) => {
        try {
            const { objectNames } = input;
            // Delete multiple objects
            const deleteErrors = await minioClient.removeObjects(DEFAULT_BUCKET, objectNames);
            if (deleteErrors.length > 0) {
                logger.error('Some files failed to delete:', deleteErrors);
                throw new Error('Failed to delete some files');
            }
            logger.info(`Deleted files: ${objectNames.join(', ')}`);
            return { success: true, deletedCount: objectNames.length };
        }
        catch (error) {
            logger.error('Error deleting files:', error);
            throw new Error('Failed to delete files');
        }
    }),
    // Get file download URL
    getDownloadUrl: publicProcedure
        .input(GetFileSchema)
        .query(async ({ input }) => {
        try {
            const { objectName } = input;
            // Generate presigned URL for download (expires in 1 hour)
            const downloadUrl = await minioClient.presignedGetObject(DEFAULT_BUCKET, objectName, 3600);
            return {
                url: downloadUrl,
                expires: new Date(Date.now() + 3600000).toISOString(),
            };
        }
        catch (error) {
            logger.error('Error generating download URL:', error);
            throw new Error('Failed to generate download URL');
        }
    }),
    // Create folder
    createFolder: publicProcedure
        .input(CreateFolderSchema)
        .mutation(async ({ input }) => {
        try {
            const { folderPath } = input;
            const normalizedPath = folderPath.replace(/^\/+|\/+$/g, '') + '/';
            // Create an empty object to represent the folder
            await minioClient.putObject(DEFAULT_BUCKET, normalizedPath, '', 0);
            logger.info(`Created folder: ${normalizedPath}`);
            return {
                id: normalizedPath,
                name: folderPath.split('/').pop() || folderPath,
                type: 'folder',
                lastModified: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Error creating folder:', error);
            throw new Error('Failed to create folder');
        }
    }),
    // Get file metadata
    getMetadata: publicProcedure
        .input(GetFileSchema)
        .query(async ({ input }) => {
        try {
            const { objectName } = input;
            const stat = await minioClient.statObject(DEFAULT_BUCKET, objectName);
            return {
                id: objectName,
                name: objectName.split('/').pop() || objectName,
                type: 'file',
                size: stat.size,
                lastModified: stat.lastModified?.toISOString() || new Date().toISOString(),
                mimeType: stat.metaData?.['content-type'] || getMimeType(objectName),
                url: getFileUrl(objectName),
                etag: stat.etag,
                metadata: stat.metaData,
            };
        }
        catch (error) {
            logger.error('Error getting file metadata:', error);
            throw new Error('Failed to get file metadata');
        }
    }),
});
// Helper function to determine MIME type based on file extension
function getMimeType(fileName) {
    const ext = fileName.toLowerCase().split('.').pop();
    const mimeTypes = {
        // Images
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        webp: 'image/webp',
        svg: 'image/svg+xml',
        // Documents
        pdf: 'application/pdf',
        doc: 'application/msword',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        xls: 'application/vnd.ms-excel',
        xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ppt: 'application/vnd.ms-powerpoint',
        pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        // Text
        txt: 'text/plain',
        json: 'application/json',
        xml: 'application/xml',
        csv: 'text/csv',
        // Archives
        zip: 'application/zip',
        rar: 'application/vnd.rar',
        tar: 'application/x-tar',
        gz: 'application/gzip',
        // Audio
        mp3: 'audio/mpeg',
        wav: 'audio/wav',
        ogg: 'audio/ogg',
        // Video
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        // Code
        js: 'application/javascript',
        ts: 'application/typescript',
        html: 'text/html',
        css: 'text/css',
    };
    return mimeTypes[ext || ''] || 'application/octet-stream';
}
//# sourceMappingURL=files.js.map