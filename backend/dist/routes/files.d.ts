export declare const filesRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    list: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            path?: string | undefined;
            recursive?: boolean | undefined;
        } | undefined;
        output: unknown;
    }>;
    stats: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: unknown;
    }>;
    upload: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            fileName: string;
            fileContent: string;
            path?: string | undefined;
            contentType?: string | undefined;
        };
        output: {
            id: string;
            name: string;
            type: "file";
            size: number;
            lastModified: string;
            mimeType: string;
            url: string;
            etag: string;
        };
    }>;
    delete: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            objectNames: string[];
        };
        output: {
            success: boolean;
            deletedCount: number;
        };
    }>;
    getDownloadUrl: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            objectName: string;
        };
        output: {
            url: string;
            expires: string;
        };
    }>;
    createFolder: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            folderPath: string;
        };
        output: {
            id: string;
            name: string;
            type: "folder";
            lastModified: string;
        };
    }>;
    getMetadata: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            objectName: string;
        };
        output: {
            id: string;
            name: string;
            type: "file";
            size: number;
            lastModified: string;
            mimeType: any;
            url: string;
            etag: string;
            metadata: import("minio").ItemBucketMetadata;
        };
    }>;
}>;
//# sourceMappingURL=files.d.ts.map