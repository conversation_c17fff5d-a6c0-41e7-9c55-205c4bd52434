import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { logger } from '../utils/logger.js';
import { modelManagementService } from '../services/model-management.js';
import { db } from '../db/index.js';
import { aiModels } from '../db/schemas/models.js';
import { eq } from 'drizzle-orm';
// Schema definitions
const ModelTypeSchema = z.enum(['embedding', 'llm', 'classification', 'generation', 'other']);
const ModelStatusSchema = z.enum(['idle', 'loading', 'running', 'error']);
const TrainingStatusSchema = z.enum(['pending', 'running', 'completed', 'failed']);
const AIModelSchema = z.object({
    id: z.string(),
    name: z.string(),
    type: ModelTypeSchema,
    version: z.string(),
    status: ModelStatusSchema,
    accuracy: z.number().optional(),
    lastTrained: z.string().optional(),
    datasetSize: z.number().optional(),
    framework: z.string(),
    size: z.string(),
    parameters: z.string().optional(),
    description: z.string().optional(),
});
const TrainingJobSchema = z.object({
    id: z.string(),
    modelId: z.string(),
    status: TrainingStatusSchema,
    progress: z.number(),
    startTime: z.string(),
    endTime: z.string().optional(),
    epochs: z.number(),
    currentEpoch: z.number(),
    loss: z.number(),
    accuracy: z.number(),
});
const ModelMetricsSchema = z.object({
    accuracy: z.number(),
    precision: z.number(),
    recall: z.number(),
    f1Score: z.number(),
    latency: z.number(),
    throughput: z.number(),
    memoryUsage: z.number(),
    cpuUsage: z.number(),
});
// Input schemas
const ModelActionSchema = z.object({
    modelId: z.string(),
});
const TrainingConfigSchema = z.object({
    modelId: z.string(),
    epochs: z.number().min(1).max(1000),
    batchSize: z.number().min(1).max(1024),
    learningRate: z.number().min(0.0001).max(1).optional(),
    datasetPath: z.string().optional(),
});
export const modelsRouter = router({
    // Get all models
    getModels: publicProcedure.query(async () => {
        try {
            const models = await modelManagementService.getAllModels();
            return {
                success: true,
                models,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get models:', error);
            throw new Error(`Failed to get models: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Get specific model
    getModel: publicProcedure
        .input(z.object({ modelId: z.string() }))
        .query(async ({ input }) => {
        try {
            const model = await modelManagementService.getModelById(input.modelId);
            if (!model) {
                throw new Error('Model not found');
            }
            return {
                success: true,
                model,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get model:', error);
            throw new Error(`Failed to get model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Get model metrics
    getModelMetrics: publicProcedure
        .input(z.object({ modelId: z.string() }))
        .query(async ({ input }) => {
        try {
            const metrics = await modelManagementService.getModelMetrics(input.modelId);
            return {
                success: true,
                metrics,
                modelId: input.modelId,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get model metrics:', error);
            throw new Error(`Failed to get model metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Deploy model
    deployModel: publicProcedure
        .input(ModelActionSchema)
        .mutation(async ({ input }) => {
        try {
            const result = await modelManagementService.deployModel(input.modelId);
            logger.info(`Model ${input.modelId} deployment initiated`);
            return {
                success: result.success,
                containerId: result.containerId,
                message: `Model ${input.modelId} deployed successfully`,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to deploy model:', error);
            throw new Error(`Failed to deploy model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Stop model
    stopModel: publicProcedure
        .input(ModelActionSchema)
        .mutation(async ({ input }) => {
        try {
            const result = await modelManagementService.stopModel(input.modelId);
            logger.info(`Model ${input.modelId} stopped`);
            return {
                success: result.success,
                message: `Model ${input.modelId} stopped successfully`,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to stop model:', error);
            throw new Error(`Failed to stop model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Get training jobs
    getTrainingJobs: publicProcedure.query(async () => {
        try {
            const jobs = await modelManagementService.getTrainingJobs();
            return {
                success: true,
                jobs,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get training jobs:', error);
            throw new Error(`Failed to get training jobs: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Start training
    startTraining: publicProcedure
        .input(TrainingConfigSchema)
        .mutation(async ({ input }) => {
        try {
            const { jobId } = await modelManagementService.startTraining(input.modelId, {
                epochs: input.epochs,
                batchSize: input.batchSize,
                learningRate: input.learningRate,
                datasetPath: input.datasetPath,
            });
            logger.info(`Training job ${jobId} started for model ${input.modelId}`);
            return {
                success: true,
                jobId,
                message: `Training started for model ${input.modelId}`,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to start training:', error);
            throw new Error(`Failed to start training: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Get training job status
    getTrainingJob: publicProcedure
        .input(z.object({ jobId: z.string() }))
        .query(async ({ input }) => {
        try {
            const job = await modelManagementService.getTrainingJob(input.jobId);
            if (!job) {
                throw new Error('Training job not found');
            }
            return {
                success: true,
                job,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get training job:', error);
            throw new Error(`Failed to get training job: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Delete model
    deleteModel: publicProcedure
        .input(ModelActionSchema)
        .mutation(async ({ input }) => {
        try {
            // First stop the model if it's running
            await modelManagementService.stopModel(input.modelId);
            // TODO: Implement actual model deletion from database
            // This would involve removing model files, database records, etc.
            // Stop the model if it's running
            await modelManagementService.stopModel(input.modelId);
            // Delete model from database
            const deletedModels = await db
                .delete(aiModels)
                .where(eq(aiModels.id, input.modelId))
                .returning();
            if (deletedModels.length === 0) {
                throw new Error('Model not found or already deleted');
            }
            // TODO: Delete model files from storage (MinIO, local filesystem, etc.)
            // This would involve checking model.modelPath and removing the files
            logger.info(`Model ${input.modelId} deleted successfully`);
            return {
                success: true,
                message: `Model ${input.modelId} deletion initiated`,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to delete model:', error);
            throw new Error(`Failed to delete model: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
});
//# sourceMappingURL=models.js.map