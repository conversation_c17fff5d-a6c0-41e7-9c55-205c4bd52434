export declare const appRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, import("@trpc/server/unstable-core-do-not-import").DecorateCreateRouterOptions<{
    auth: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        register: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                email: string;
                password: string;
                firstName?: string | undefined;
                lastName?: string | undefined;
                username?: string | undefined;
            };
            output: {
                user: {
                    id: string;
                    email: string;
                    firstName: string | null;
                    lastName: string | null;
                    username: string | null;
                };
                sessionId: string;
            };
        }>;
        login: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                email: string;
                password: string;
            };
            output: {
                user: {
                    id: string;
                    email: string;
                    firstName: string | null;
                    lastName: string | null;
                    username: string | null;
                    role: string;
                };
                sessionId: string;
            };
        }>;
        logout: import("@trpc/server").TRPCMutationProcedure<{
            input: void;
            output: {
                success: boolean;
            };
        }>;
        session: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                user: {
                    id: string;
                    email: string;
                    firstName: string | null;
                    lastName: string | null;
                    username: string | null;
                    role: string;
                    avatar: string | null;
                };
                session: {
                    id: string;
                    expiresAt: Date;
                };
            } | null;
        }>;
        changePassword: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                currentPassword: string;
                newPassword: string;
            };
            output: {
                success: boolean;
            };
        }>;
    }>;
    users: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        me: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                id: string;
                email: string;
                username: string | null;
                passwordHash: string | null;
                firstName: string | null;
                lastName: string | null;
                avatar: string | null;
                bio: string | null;
                role: string;
                isActive: boolean;
                isVerified: boolean;
                lastLogin: Date | null;
                preferences: unknown;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        getById: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
            };
            output: {
                id: string;
                username: string | null;
                firstName: string | null;
                lastName: string | null;
                avatar: string | null;
                bio: string | null;
                createdAt: Date;
            };
        }>;
        updateProfile: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                firstName?: string | undefined;
                lastName?: string | undefined;
                bio?: string | undefined;
                avatar?: string | undefined;
            };
            output: {
                id: string;
                email: string;
                username: string | null;
                passwordHash: string | null;
                firstName: string | null;
                lastName: string | null;
                avatar: string | null;
                bio: string | null;
                role: string;
                isActive: boolean;
                isVerified: boolean;
                lastLogin: Date | null;
                preferences: unknown;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        list: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                limit?: number | undefined;
                offset?: number | undefined;
                search?: string | undefined;
            };
            output: {
                id: string;
                email: string;
                username: string | null;
                firstName: string | null;
                lastName: string | null;
                role: string;
                isActive: boolean;
                isVerified: boolean;
                createdAt: Date;
                lastLogin: Date | null;
            }[];
        }>;
    }>;
    system: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        health: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                status: string;
                timestamp: string;
                services: {
                    redis: string;
                    postgres: string;
                    api: string;
                };
                error?: undefined;
            } | {
                status: string;
                timestamp: string;
                error: string;
                services: {
                    redis: string;
                    postgres: string;
                    api: string;
                };
            };
        }>;
        serviceStatus: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                services: {
                    id: string;
                    name: string;
                    status: string;
                    port: number;
                }[];
            };
        }>;
        stats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                totalUsers: number;
                activeUsers: number;
                totalRequests: number;
                responseTime: string;
                uptime: number;
                memory: NodeJS.MemoryUsage;
                timestamp: string;
            };
        }>;
        restartService: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                serviceId: string;
            };
            output: {
                success: boolean;
                message: string;
                timestamp: string;
            };
        }>;
    }>;
    realData: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, import("@trpc/server/unstable-core-do-not-import").DecorateCreateRouterOptions<{
        docker: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
            ctx: {
                db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                    $client: import("postgres").Sql<{}>;
                };
                user: import("../auth/index.js").User | null;
                session: import("../auth/index.js").Session | null;
                req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
                res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
            };
            meta: object;
            errorShape: {
                data: {
                    zodError: string | null;
                    code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                    httpStatus: number;
                    path?: string;
                    stack?: string;
                };
                message: string;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
            };
            transformer: true;
        }, {
            status: import("@trpc/server").TRPCQueryProcedure<{
                input: void;
                output: {
                    success: boolean;
                    data: {
                        running: boolean;
                        message: string;
                    };
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            containers: import("@trpc/server").TRPCQueryProcedure<{
                input: {
                    all?: boolean | undefined;
                };
                output: {
                    success: boolean;
                    data: import("../services/docker.js").ContainerInfo[];
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            container: import("@trpc/server").TRPCQueryProcedure<{
                input: {
                    id: string;
                };
                output: {
                    success: boolean;
                    data: import("../services/docker.js").ContainerInfo | null;
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            containerAction: import("@trpc/server").TRPCMutationProcedure<{
                input: {
                    id: string;
                    action: "start" | "stop" | "restart";
                };
                output: {
                    success: boolean;
                    data: {
                        action: "start" | "stop" | "restart";
                        containerId: string;
                        result: boolean;
                    };
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            logs: import("@trpc/server").TRPCQueryProcedure<{
                input: {
                    id: string;
                    tail?: number | undefined;
                };
                output: {
                    success: boolean;
                    data: {
                        containerId: string;
                        logs: string;
                        tail: number;
                    };
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
        }>;
        system: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
            ctx: {
                db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                    $client: import("postgres").Sql<{}>;
                };
                user: import("../auth/index.js").User | null;
                session: import("../auth/index.js").Session | null;
                req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
                res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
            };
            meta: object;
            errorShape: {
                data: {
                    zodError: string | null;
                    code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                    httpStatus: number;
                    path?: string;
                    stack?: string;
                };
                message: string;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
            };
            transformer: true;
        }, {
            metrics: import("@trpc/server").TRPCQueryProcedure<{
                input: void;
                output: {
                    success: boolean;
                    data: import("../services/system-monitor.js").SystemMetrics;
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            health: import("@trpc/server").TRPCQueryProcedure<{
                input: void;
                output: {
                    success: boolean;
                    data: {
                        status: string;
                        services: (import("../services/system-monitor.js").ServiceHealth | {
                            name: string;
                            status: string;
                            lastCheck: string;
                            error: string | undefined;
                        })[];
                        docker: {
                            running: boolean;
                        };
                        timestamp: string;
                    };
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            processes: import("@trpc/server").TRPCQueryProcedure<{
                input: void;
                output: {
                    success: boolean;
                    data: {
                        pid: number;
                        name: string;
                        cpu: number;
                        memory: number;
                        command: string;
                    }[];
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
            stats: import("@trpc/server").TRPCQueryProcedure<{
                input: void;
                output: {
                    success: boolean;
                    data: {
                        system: {
                            uptime: string;
                            hostname: string;
                            platform: string;
                            arch: string;
                        };
                        resources: {
                            cpu: {
                                usage: number;
                                cores: number;
                                temperature: number | undefined;
                            };
                            memory: {
                                used: string;
                                total: string;
                                percentage: number;
                            };
                            disk: {
                                used: string;
                                total: string;
                                percentage: number;
                            };
                            network: {
                                in: string;
                                out: string;
                            };
                        };
                        containers: {
                            total: number;
                            running: number;
                            stopped: number;
                        };
                        docker: {
                            running: boolean;
                        };
                    };
                    error?: undefined;
                } | {
                    success: boolean;
                    error: string;
                    data?: undefined;
                };
            }>;
        }>;
    }>>;
    vector: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        health: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                status: string;
                connected: boolean;
                timestamp: string;
            };
        }>;
        connect: import("@trpc/server").TRPCMutationProcedure<{
            input: void;
            output: {
                success: boolean;
                message: string;
            };
        }>;
        listCollections: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: ({
                name: string;
                statistics: any;
                loaded: boolean;
                error?: undefined;
            } | {
                name: string;
                statistics: null;
                loaded: boolean;
                error: string;
            })[];
        }>;
        createCollection: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
                dimension: number;
                description?: string | undefined;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        dropCollection: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        hasCollection: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                name: string;
            };
            output: {
                exists: boolean;
            };
        }>;
        loadCollection: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        releaseCollection: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        createIndex: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                collectionName: string;
                params?: Record<string, any> | undefined;
                fieldName?: string | undefined;
                indexType?: string | undefined;
                metricType?: "L2" | "IP" | "COSINE" | undefined;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        insert: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                collectionName: string;
                documents: {
                    id: string;
                    vector: number[];
                    metadata?: Record<string, any> | undefined;
                }[];
            };
            output: {
                success: boolean;
                message: string;
                count: number;
            };
        }>;
        search: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                collectionName: string;
                queryVectors: number[][];
                params?: Record<string, any> | undefined;
                topK?: number | undefined;
            };
            output: {
                success: boolean;
                results: import("../services/vector/milvus.js").VectorSearchResult[][];
                totalQueries: number;
            };
        }>;
        query: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                expr: string;
                collectionName: string;
                outputFields?: string[] | undefined;
            };
            output: {
                success: boolean;
                results: any[];
                count: number;
            };
        }>;
        delete: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                ids: string[];
                collectionName: string;
            };
            output: {
                success: boolean;
                message: string;
                count: number;
            };
        }>;
        getCollectionStatistics: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                name: string;
            };
            output: {
                success: boolean;
                statistics: any;
                collectionName: string;
            };
        }>;
        flush: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
    }>;
    docker: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        isRunning: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                running: boolean;
                timestamp: string;
                error?: undefined;
            } | {
                running: boolean;
                error: string;
                timestamp: string;
            };
        }>;
        listContainers: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                all?: boolean | undefined;
            } | undefined;
            output: {
                success: boolean;
                containers: import("../services/docker.js").ContainerInfo[];
                count: number;
            };
        }>;
        getContainer: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                container: import("../services/docker.js").ContainerInfo;
            };
        }>;
        startContainer: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        stopContainer: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        restartContainer: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        removeContainer: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        getContainerLogs: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
                tail?: number | undefined;
            };
            output: {
                success: boolean;
                logs: string;
                containerId: string;
                lines: number;
            };
        }>;
        startMultipleContainers: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                ids: string[];
            };
            output: {
                success: boolean;
                message: string;
                successful: number;
                failed: number;
                total: number;
            };
        }>;
        stopMultipleContainers: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                ids: string[];
            };
            output: {
                success: boolean;
                message: string;
                successful: number;
                failed: number;
                total: number;
            };
        }>;
        composeUp: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                services?: string[] | undefined;
                detached?: boolean | undefined;
                build?: boolean | undefined;
                recreate?: boolean | undefined;
            } | undefined;
            output: {
                success: boolean;
                message: string;
                output: string;
                options: {
                    detached: boolean;
                    build: boolean;
                    recreate: boolean;
                    services?: string[] | undefined;
                } | undefined;
            };
        }>;
        composeDown: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                removeVolumes?: boolean | undefined;
                removeImages?: boolean | undefined;
                removeOrphans?: boolean | undefined;
            } | undefined;
            output: {
                success: boolean;
                message: string;
                output: string;
                options: {
                    removeVolumes: boolean;
                    removeImages: boolean;
                    removeOrphans: boolean;
                } | undefined;
            };
        }>;
        composeBuild: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                service?: string | undefined;
                noCache?: boolean | undefined;
            } | undefined;
            output: {
                success: boolean;
                message: string;
                output: string;
            };
        }>;
        composePull: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                service?: string | undefined;
            } | undefined;
            output: {
                success: boolean;
                message: string;
                output: string;
            };
        }>;
        getComposeStatus: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                status: {
                    isRunning: boolean;
                    services: number;
                    runningServices: number;
                    containers: Array<{
                        name: string;
                        service: string;
                        status: string;
                        health?: string;
                    }>;
                };
            };
        }>;
        getComposeServices: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                services: {
                    name: string;
                    image: string;
                    status: string;
                    ports: string[];
                }[];
            };
        }>;
        getImages: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                images: {
                    id: string;
                    repository: string;
                    tag: string;
                    size: number;
                    created: string;
                }[];
                count: number;
            };
        }>;
        removeImage: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
                force?: boolean | undefined;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        getNetworks: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                networks: {
                    id: string;
                    name: string;
                    driver: string;
                    scope: string;
                    created: string;
                    containers: number;
                }[];
                count: number;
            };
        }>;
        getVolumes: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                volumes: {
                    name: string;
                    driver: string;
                    mountpoint: string;
                    created: string;
                    size?: number;
                }[];
                count: number;
            };
        }>;
        removeVolume: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                name: string;
                force?: boolean | undefined;
            };
            output: {
                success: boolean;
                message: string;
            };
        }>;
        getContainerStats: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                stats: {
                    cpuUsage: number;
                    memoryUsage: number;
                    memoryLimit: number;
                    networkRx: number;
                    networkTx: number;
                } | undefined;
                containerId: string;
                timestamp: string;
            };
        }>;
        getSystemInfo: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                info: {
                    dockerRunning: boolean;
                    totalContainers: number;
                    runningContainers: number;
                    stoppedContainers: number;
                    systemDf: {
                        containers: {
                            active: number;
                            total: number;
                            size: number;
                            reclaimable: number;
                        };
                        images: {
                            active: number;
                            total: number;
                            size: number;
                            reclaimable: number;
                        };
                        volumes: {
                            active: number;
                            total: number;
                            size: number;
                            reclaimable: number;
                        };
                    };
                    timestamp: string;
                };
            };
        }>;
        getSystemDf: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                df: {
                    containers: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                    images: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                    volumes: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                };
                timestamp: string;
            };
        }>;
    }>;
    database: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        health: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                status: string;
                connected: boolean;
                timestamp: string;
                testQuery: boolean;
                error?: undefined;
            } | {
                status: string;
                connected: boolean;
                timestamp: string;
                error: string;
                testQuery?: undefined;
            };
        }>;
        testConnection: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                username?: string | undefined;
                host?: string | undefined;
                port?: number | undefined;
                database?: string | undefined;
                password?: string | undefined;
            };
            output: {
                success: boolean;
                message: string;
                timestamp: string;
            };
        }>;
        getTables: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                tables: {
                    name: string;
                    type: string;
                    schema: string;
                }[];
            };
        }>;
        getTableSchema: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                tableName: string;
            };
            output: {
                success: boolean;
                tableName: "users" | "sessions" | "ai_models" | "training_jobs" | "model_metrics";
                columns: {
                    name: any;
                    type: any;
                    nullable: boolean;
                    default: any;
                    maxLength: any;
                    precision: any;
                    scale: any;
                }[];
            };
        }>;
        getTableIndexes: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                tableName: string;
            };
            output: {
                success: boolean;
                tableName: string;
                indexes: {
                    name: any;
                    column: any;
                    unique: any;
                    primary: any;
                }[];
            };
        }>;
        executeQuery: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                query: string;
                params?: any[] | undefined;
            };
            output: {
                success: boolean;
                query: string;
                rows: import("postgres").RowList<Record<string, unknown>[]>;
                rowCount: number;
                executionTime: number;
                timestamp: string;
            };
        }>;
        getTableData: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                tableName: string;
                orderBy?: string | undefined;
                limit?: number | undefined;
                offset?: number | undefined;
                orderDirection?: "desc" | "asc" | undefined;
                filters?: Record<string, any> | undefined;
            };
            output: {
                success: boolean;
                tableName: "users" | "sessions" | "ai_models" | "training_jobs" | "model_metrics";
                data: import("postgres").RowList<Record<string, unknown>[]>;
                pagination: {
                    limit: number;
                    offset: number;
                    total: number;
                    pages: number;
                    currentPage: number;
                };
                executionTime: number;
                timestamp: string;
            };
        }>;
        getDatabaseStats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                database: {
                    name: unknown;
                    size: unknown;
                };
                tables: {
                    schema: any;
                    name: any;
                    stats: {
                        inserts: number;
                        updates: number;
                        deletes: number;
                        liveTuples: number;
                        deadTuples: number;
                        lastVacuum: any;
                        lastAutoVacuum: any;
                        lastAnalyze: any;
                        lastAutoAnalyze: any;
                    };
                }[];
                timestamp: string;
            };
        }>;
        getActiveConnections: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                connections: {
                    pid: any;
                    username: any;
                    applicationName: any;
                    clientAddress: any;
                    clientPort: any;
                    backendStart: any;
                    state: any;
                    queryStart: any;
                    query: any;
                }[];
                count: number;
                timestamp: string;
            };
        }>;
    }>;
    etcd: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        health: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                status: string;
                connected: boolean;
                timestamp: string;
                version?: string;
            };
        }>;
        getAllKeys: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                keys: import("../services/etcd.js").KeyValuePair[];
                count: number;
            };
        }>;
        getKey: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                key: string;
            };
            output: {
                success: boolean;
                message: string;
            } | {
                key: string;
                value: string;
                createRevision: number;
                modRevision: number;
                version: number;
                success: boolean;
                message?: undefined;
            };
        }>;
        putKey: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                value: string;
                key: string;
            };
            output: {
                success: boolean;
                message: string;
                key: string;
                value: string;
            };
        }>;
        deleteKey: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                key: string;
            };
            output: {
                success: boolean;
                message: string;
                key?: undefined;
            } | {
                success: boolean;
                message: string;
                key: string;
            };
        }>;
        getKeysByPrefix: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                prefix: string;
            };
            output: {
                success: boolean;
                keys: import("../services/etcd.js").KeyValuePair[];
                count: number;
                prefix: string;
            };
        }>;
        discoverServices: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                services: {
                    serviceName: string;
                    instances: {
                        lastSeenAgo: number;
                        id: string;
                        host: string;
                        port: number;
                        healthy: boolean;
                        lastSeen: string;
                        metadata?: Record<string, any>;
                        ttl?: number;
                    }[];
                    totalInstances: number;
                    healthyInstances: number;
                }[];
                totalServices: number;
                timestamp: string;
            };
        }>;
        getServiceInstances: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                serviceName: string;
            };
            output: {
                success: boolean;
                serviceName: string;
                instances: {
                    lastSeenAgo: number;
                    id: string;
                    host: string;
                    port: number;
                    healthy: boolean;
                    lastSeen: string;
                    metadata?: Record<string, any>;
                    ttl?: number;
                }[];
                count: number;
                healthyCount: number;
            };
        }>;
        registerService: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                serviceName: string;
                serviceUrl: string;
                metadata?: Record<string, any> | undefined;
                ttl?: number | undefined;
            };
            output: {
                success: boolean;
                message: string;
                serviceId: string;
                serviceName: string;
            };
        }>;
        deregisterService: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                serviceId: string;
                serviceName: string;
            };
            output: {
                success: boolean;
                message: string;
                serviceName?: undefined;
                serviceId?: undefined;
            } | {
                success: boolean;
                message: string;
                serviceName: string;
                serviceId: string;
            };
        }>;
        getConfigurationTemplates: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                templates: {
                    name: string;
                    description: string;
                    keys: {
                        key: string;
                        value: string;
                        description: string;
                    }[];
                }[];
                count: number;
            };
        }>;
        applyConfigurationTemplate: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                values: Record<string, string>;
                templateName: string;
            };
            output: {
                success: boolean;
                message: string;
                appliedKeys: string[];
                count: number;
            };
        }>;
        getClusterInfo: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                cluster: import("../services/etcd.js").ClusterInfo;
                memberCount: number;
                timestamp: string;
            };
        }>;
        watchKey: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                key: string;
            };
            output: {
                timestamp: string;
                key: string;
                value: string | null;
                watching: boolean;
                watchId: string;
                success: boolean;
            };
        }>;
    }>;
    models: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        getModels: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                models: {
                    id: string;
                    name: string;
                    createdAt: Date;
                    updatedAt: Date;
                    description: string | null;
                    type: "embedding" | "llm" | "classification" | "generation" | "other";
                    version: string;
                    status: "idle" | "loading" | "running" | "error";
                    accuracy: string | null;
                    lastTrained: Date | null;
                    datasetSize: number | null;
                    framework: string;
                    size: string;
                    parameters: string | null;
                    dockerImage: string | null;
                    containerId: string | null;
                    modelPath: string | null;
                    config: unknown;
                }[];
                timestamp: string;
            };
        }>;
        getModel: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                modelId: string;
            };
            output: {
                success: boolean;
                model: {
                    id: string;
                    name: string;
                    createdAt: Date;
                    updatedAt: Date;
                    description: string | null;
                    type: "embedding" | "llm" | "classification" | "generation" | "other";
                    version: string;
                    status: "idle" | "loading" | "running" | "error";
                    accuracy: string | null;
                    lastTrained: Date | null;
                    datasetSize: number | null;
                    framework: string;
                    size: string;
                    parameters: string | null;
                    dockerImage: string | null;
                    containerId: string | null;
                    modelPath: string | null;
                    config: unknown;
                };
                timestamp: string;
            };
        }>;
        getModelMetrics: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                modelId: string;
            };
            output: {
                success: boolean;
                metrics: any;
                modelId: string;
                timestamp: string;
            };
        }>;
        deployModel: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                modelId: string;
            };
            output: {
                success: boolean;
                containerId: string | undefined;
                message: string;
                timestamp: string;
            };
        }>;
        stopModel: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                modelId: string;
            };
            output: {
                success: boolean;
                message: string;
                timestamp: string;
            };
        }>;
        getTrainingJobs: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                jobs: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    status: "running" | "pending" | "completed" | "failed";
                    accuracy: string | null;
                    config: unknown;
                    modelId: string;
                    progress: number;
                    startTime: Date;
                    endTime: Date | null;
                    epochs: number;
                    currentEpoch: number;
                    loss: string | null;
                    batchSize: number | null;
                    learningRate: string | null;
                    datasetPath: string | null;
                    logs: string | null;
                }[];
                timestamp: string;
            };
        }>;
        startTraining: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                modelId: string;
                epochs: number;
                batchSize: number;
                learningRate?: number | undefined;
                datasetPath?: string | undefined;
            };
            output: {
                success: boolean;
                jobId: string;
                message: string;
                timestamp: string;
            };
        }>;
        getTrainingJob: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                jobId: string;
            };
            output: {
                success: boolean;
                job: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    status: "running" | "pending" | "completed" | "failed";
                    accuracy: string | null;
                    config: unknown;
                    modelId: string;
                    progress: number;
                    startTime: Date;
                    endTime: Date | null;
                    epochs: number;
                    currentEpoch: number;
                    loss: string | null;
                    batchSize: number | null;
                    learningRate: string | null;
                    datasetPath: string | null;
                    logs: string | null;
                };
                timestamp: string;
            };
        }>;
        deleteModel: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                modelId: string;
            };
            output: {
                success: boolean;
                message: string;
                timestamp: string;
            };
        }>;
    }>;
    files: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        list: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                path?: string | undefined;
                recursive?: boolean | undefined;
            } | undefined;
            output: unknown;
        }>;
        stats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: unknown;
        }>;
        upload: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                fileName: string;
                fileContent: string;
                path?: string | undefined;
                contentType?: string | undefined;
            };
            output: {
                id: string;
                name: string;
                type: "file";
                size: number;
                lastModified: string;
                mimeType: string;
                url: string;
                etag: string;
            };
        }>;
        delete: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                objectNames: string[];
            };
            output: {
                success: boolean;
                deletedCount: number;
            };
        }>;
        getDownloadUrl: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                objectName: string;
            };
            output: {
                url: string;
                expires: string;
            };
        }>;
        createFolder: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                folderPath: string;
            };
            output: {
                id: string;
                name: string;
                type: "folder";
                lastModified: string;
            };
        }>;
        getMetadata: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                objectName: string;
            };
            output: {
                id: string;
                name: string;
                type: "file";
                size: number;
                lastModified: string;
                mimeType: any;
                url: string;
                etag: string;
                metadata: import("minio").ItemBucketMetadata;
            };
        }>;
    }>;
    posts: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        list: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                limit?: number | undefined;
                offset?: number | undefined;
            };
            output: {
                posts: import("./posts.js").Post[];
                total: number;
                hasMore: boolean;
            };
        }>;
        getById: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
            };
            output: import("./posts.js").Post;
        }>;
        create: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                body: string;
                title: string;
                authorId?: string | undefined;
            };
            output: import("./posts.js").Post;
        }>;
        update: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
                body?: string | undefined;
                title?: string | undefined;
            };
            output: {
                updatedAt: Date;
                body: string;
                title: string;
                id: string;
                authorId?: string;
                createdAt: Date;
            };
        }>;
        delete: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                deletedPost: import("./posts.js").Post;
            };
        }>;
        stats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                total: number;
                recentPosts: number;
                thisWeek: number;
                authors: number;
            };
        }>;
    }>;
    logs: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        list: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                search?: string | undefined;
                limit?: number | undefined;
                offset?: number | undefined;
                level?: "error" | "debug" | "info" | "warn" | undefined;
                service?: string | undefined;
            };
            output: {
                logs: import("./logs.js").LogEntry[];
                total: number;
                hasMore: boolean;
            };
        }>;
        stats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                total: number;
                last24Hours: number;
                lastHour: number;
                errors: number;
                warnings: number;
                services: string[];
                errorRate: number;
                levels: {
                    info: number;
                    warn: number;
                    error: number;
                    debug: number;
                };
            };
        }>;
        services: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: string[];
        }>;
        clear: import("@trpc/server").TRPCMutationProcedure<{
            input: void;
            output: {
                success: boolean;
                clearedCount: number;
            };
        }>;
    }>;
    settings: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        getAll: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                general: any;
                database: any;
                security: any;
                notifications: any;
                backup: any;
                monitoring: any;
            };
        }>;
        getCategory: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                category: "database" | "general" | "security" | "notifications" | "backup" | "monitoring";
            };
            output: any;
        }>;
        updateGeneral: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                appName: string;
                appDescription: string;
                timezone: string;
                theme: "light" | "dark" | "auto";
                language: string;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    appName: string;
                    appDescription: string;
                    timezone: string;
                    theme: "light" | "dark" | "auto";
                    language: string;
                };
            };
        }>;
        updateDatabase: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                logLevel: "error" | "debug" | "info" | "warn";
                maxConnections: number;
                connectionTimeout: number;
                queryTimeout: number;
                enableLogging: boolean;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    logLevel: "error" | "debug" | "info" | "warn";
                    maxConnections: number;
                    connectionTimeout: number;
                    queryTimeout: number;
                    enableLogging: boolean;
                };
            };
        }>;
        updateSecurity: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                sessionTimeout: number;
                maxLoginAttempts: number;
                enableTwoFactor: boolean;
                passwordMinLength: number;
                requireSpecialChars: boolean;
                allowSignups: boolean;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    sessionTimeout: number;
                    maxLoginAttempts: number;
                    enableTwoFactor: boolean;
                    passwordMinLength: number;
                    requireSpecialChars: boolean;
                    allowSignups: boolean;
                };
            };
        }>;
        updateNotifications: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                emailEnabled: boolean;
                slackEnabled: boolean;
                webhookEnabled: boolean;
                emailRecipients: string[];
                notifyOnErrors: boolean;
                notifyOnBackups: boolean;
                notifyOnDeployments: boolean;
                slackWebhook?: string | undefined;
                webhookUrl?: string | undefined;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    emailEnabled: boolean;
                    slackEnabled: boolean;
                    webhookEnabled: boolean;
                    emailRecipients: string[];
                    notifyOnErrors: boolean;
                    notifyOnBackups: boolean;
                    notifyOnDeployments: boolean;
                    slackWebhook?: string | undefined;
                    webhookUrl?: string | undefined;
                };
            };
        }>;
        updateBackup: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                enabled: boolean;
                schedule: string;
                retentionDays: number;
                storageLocation: "local" | "s3" | "gcp" | "azure";
                encryptBackups: boolean;
                compressionLevel: number;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    enabled: boolean;
                    schedule: string;
                    retentionDays: number;
                    storageLocation: "local" | "s3" | "gcp" | "azure";
                    encryptBackups: boolean;
                    compressionLevel: number;
                };
            };
        }>;
        updateMonitoring: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                metricsEnabled: boolean;
                logRetentionDays: number;
                alertingEnabled: boolean;
                performanceTracking: boolean;
                errorTracking: boolean;
                uptimeMonitoring: boolean;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    metricsEnabled: boolean;
                    logRetentionDays: number;
                    alertingEnabled: boolean;
                    performanceTracking: boolean;
                    errorTracking: boolean;
                    uptimeMonitoring: boolean;
                };
            };
        }>;
        resetToDefaults: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                category?: "database" | "general" | "security" | "notifications" | "backup" | "monitoring" | undefined;
            };
            output: {
                success: boolean;
                message: string;
                data: {
                    appName: string;
                    appDescription: string;
                    timezone: string;
                    theme: "auto";
                    language: string;
                } | {
                    maxConnections: number;
                    connectionTimeout: number;
                    queryTimeout: number;
                    enableLogging: boolean;
                    logLevel: "info";
                } | {
                    sessionTimeout: number;
                    maxLoginAttempts: number;
                    enableTwoFactor: boolean;
                    passwordMinLength: number;
                    requireSpecialChars: boolean;
                    allowSignups: boolean;
                } | {
                    emailEnabled: boolean;
                    slackEnabled: boolean;
                    webhookEnabled: boolean;
                    emailRecipients: never[];
                    notifyOnErrors: boolean;
                    notifyOnBackups: boolean;
                    notifyOnDeployments: boolean;
                } | {
                    enabled: boolean;
                    schedule: string;
                    retentionDays: number;
                    storageLocation: "local";
                    encryptBackups: boolean;
                    compressionLevel: number;
                } | {
                    metricsEnabled: boolean;
                    logRetentionDays: number;
                    alertingEnabled: boolean;
                    performanceTracking: boolean;
                    errorTracking: boolean;
                    uptimeMonitoring: boolean;
                };
            } | {
                success: boolean;
                message: string;
                data: {
                    general: {
                        appName: string;
                        appDescription: string;
                        timezone: string;
                        theme: "auto";
                        language: string;
                    };
                    database: {
                        maxConnections: number;
                        connectionTimeout: number;
                        queryTimeout: number;
                        enableLogging: boolean;
                        logLevel: "info";
                    };
                    security: {
                        sessionTimeout: number;
                        maxLoginAttempts: number;
                        enableTwoFactor: boolean;
                        passwordMinLength: number;
                        requireSpecialChars: boolean;
                        allowSignups: boolean;
                    };
                    notifications: {
                        emailEnabled: boolean;
                        slackEnabled: boolean;
                        webhookEnabled: boolean;
                        emailRecipients: never[];
                        notifyOnErrors: boolean;
                        notifyOnBackups: boolean;
                        notifyOnDeployments: boolean;
                    };
                    backup: {
                        enabled: boolean;
                        schedule: string;
                        retentionDays: number;
                        storageLocation: "local";
                        encryptBackups: boolean;
                        compressionLevel: number;
                    };
                    monitoring: {
                        metricsEnabled: boolean;
                        logRetentionDays: number;
                        alertingEnabled: boolean;
                        performanceTracking: boolean;
                        errorTracking: boolean;
                        uptimeMonitoring: boolean;
                    };
                };
            };
        }>;
        export: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                settings: any;
                exportedAt: string;
                version: string;
            };
        }>;
        importSettings: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                settings: Record<string, any>;
                overwrite?: boolean | undefined;
            };
            output: {
                success: boolean;
                message: string;
                imported: string[];
            };
        }>;
        getSystemInfo: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                nodeVersion: string;
                platform: NodeJS.Platform;
                arch: NodeJS.Architecture;
                uptime: number;
                memory: NodeJS.MemoryUsage;
                env: string;
                timezones: string[];
                languages: {
                    code: string;
                    name: string;
                }[];
            };
        }>;
    }>;
}>>;
export type AppRouter = typeof appRouter;
//# sourceMappingURL=index.d.ts.map