import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
// Input schemas
const getPostSchema = z.object({
    id: z.string()
});
const createPostSchema = z.object({
    title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
    body: z.string().min(1, 'Body is required').max(2000, 'Body too long'),
    authorId: z.string().optional()
});
const updatePostSchema = z.object({
    id: z.string(),
    title: z.string().min(1, 'Title is required').max(200, 'Title too long').optional(),
    body: z.string().min(1, 'Body is required').max(2000, 'Body too long').optional()
});
const listPostsSchema = z.object({
    limit: z.number().min(1).max(100).optional().default(20),
    offset: z.number().min(0).optional().default(0)
});
let posts = [
    {
        id: '1',
        title: 'Welcome to the Real Data System',
        body: 'This is a sample post created with real backend data instead of JSONPlaceholder. This demonstrates the transition from mock data to real data in our application.',
        authorId: 'system',
        createdAt: new Date('2024-01-01T00:00:00Z'),
        updatedAt: new Date('2024-01-01T00:00:00Z')
    },
    {
        id: '2',
        title: 'System Architecture Overview',
        body: 'Our system now uses real backend APIs powered by tRPC, FastAPI, and modern TypeScript. This post explains the architecture and data flow.',
        authorId: 'system',
        createdAt: new Date('2024-01-02T00:00:00Z'),
        updatedAt: new Date('2024-01-02T00:00:00Z')
    },
    {
        id: '3',
        title: 'Real-time Monitoring Features',
        body: 'The dashboard now includes real-time system monitoring, live metrics, and actual health checks from running services.',
        authorId: 'system',
        createdAt: new Date('2024-01-03T00:00:00Z'),
        updatedAt: new Date('2024-01-03T00:00:00Z')
    },
    {
        id: '4',
        title: 'User Management System',
        body: 'User management now connects to the real database with proper authentication, authorization, and user lifecycle management.',
        authorId: 'system',
        createdAt: new Date('2024-01-04T00:00:00Z'),
        updatedAt: new Date('2024-01-04T00:00:00Z')
    },
    {
        id: '5',
        title: 'File Storage Integration',
        body: 'MinIO integration provides real file storage capabilities with upload, download, and management features through the admin interface.',
        authorId: 'system',
        createdAt: new Date('2024-01-05T00:00:00Z'),
        updatedAt: new Date('2024-01-05T00:00:00Z')
    }
];
export const postsRouter = router({
    // Get all posts
    list: publicProcedure
        .input(listPostsSchema)
        .query(async ({ input }) => {
        const { limit, offset } = input;
        const sortedPosts = posts
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(offset, offset + limit);
        return {
            posts: sortedPosts,
            total: posts.length,
            hasMore: offset + limit < posts.length
        };
    }),
    // Get single post by ID
    getById: publicProcedure
        .input(getPostSchema)
        .query(async ({ input }) => {
        const post = posts.find(p => p.id === input.id);
        if (!post) {
            throw new Error('Post not found');
        }
        return post;
    }),
    // Create new post
    create: publicProcedure
        .input(createPostSchema)
        .mutation(async ({ input }) => {
        const newPost = {
            id: (posts.length + 1).toString(),
            title: input.title,
            body: input.body,
            authorId: input.authorId || 'anonymous',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        posts.push(newPost);
        return newPost;
    }),
    // Update existing post
    update: publicProcedure
        .input(updatePostSchema)
        .mutation(async ({ input }) => {
        const postIndex = posts.findIndex(p => p.id === input.id);
        if (postIndex === -1) {
            throw new Error('Post not found');
        }
        const updatedPost = {
            ...posts[postIndex],
            ...(input.title && { title: input.title }),
            ...(input.body && { body: input.body }),
            updatedAt: new Date()
        };
        posts[postIndex] = updatedPost;
        return updatedPost;
    }),
    // Delete post
    delete: publicProcedure
        .input(getPostSchema)
        .mutation(async ({ input }) => {
        const postIndex = posts.findIndex(p => p.id === input.id);
        if (postIndex === -1) {
            throw new Error('Post not found');
        }
        const deletedPost = posts[postIndex];
        posts.splice(postIndex, 1);
        return { success: true, deletedPost };
    }),
    // Get posts stats
    stats: publicProcedure
        .query(async () => {
        const now = new Date();
        const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return {
            total: posts.length,
            recentPosts: posts.filter(p => p.createdAt >= last30Days).length,
            thisWeek: posts.filter(p => p.createdAt >= last7Days).length,
            authors: Array.from(new Set(posts.map(p => p.authorId))).length
        };
    })
});
//# sourceMappingURL=posts.js.map