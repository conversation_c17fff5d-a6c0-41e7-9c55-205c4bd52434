import { TRPCError } from '@trpc/server';
import { eq } from 'drizzle-orm';
import * as v from 'valibot';
import { router, publicProcedure, protectedProcedure, adminProcedure } from '../lib/trpc';
import { users } from '../db/schemas/users';
// Input validation schemas
const getUserSchema = v.object({
    id: v.pipe(v.string(), v.uuid()),
});
const updateUserSchema = v.object({
    firstName: v.optional(v.pipe(v.string(), v.maxLength(100))),
    lastName: v.optional(v.pipe(v.string(), v.maxLength(100))),
    bio: v.optional(v.pipe(v.string(), v.maxLength(500))),
    avatar: v.optional(v.pipe(v.string(), v.url())),
});
const listUsersSchema = v.object({
    limit: v.optional(v.pipe(v.number(), v.integer(), v.minValue(1), v.maxValue(100))),
    offset: v.optional(v.pipe(v.number(), v.integer(), v.minValue(0))),
    search: v.optional(v.string()),
});
export const userRouter = router({
    // Get current user profile
    me: protectedProcedure.query(async ({ ctx }) => {
        const user = await ctx.db.query.users.findFirst({
            where: eq(users.id, ctx.user.id),
        });
        if (!user) {
            throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'User not found',
            });
        }
        // Remove sensitive data
        const { ...publicUser } = user;
        return publicUser;
    }),
    // Get user by ID (public)
    getById: publicProcedure
        .input((val) => v.parse(getUserSchema, val))
        .query(async ({ ctx, input }) => {
        const user = await ctx.db.query.users.findFirst({
            where: eq(users.id, input.id),
        });
        if (!user) {
            throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'User not found',
            });
        }
        // Return only public fields
        return {
            id: user.id,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            bio: user.bio,
            createdAt: user.createdAt,
        };
    }),
    // Update current user profile
    updateProfile: protectedProcedure
        .input((val) => v.parse(updateUserSchema, val))
        .mutation(async ({ ctx, input }) => {
        const [updatedUser] = await ctx.db
            .update(users)
            .set({
            ...input,
            updatedAt: new Date(),
        })
            .where(eq(users.id, ctx.user.id))
            .returning();
        if (!updatedUser) {
            throw new TRPCError({
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to update user',
            });
        }
        return updatedUser;
    }),
    // List all users (admin only)
    list: adminProcedure
        .input((val) => v.parse(listUsersSchema, val))
        .query(async ({ ctx, input }) => {
        const { limit = 20, offset = 0, search } = input;
        const userList = await ctx.db.select().from(users).limit(limit).offset(offset);
        return userList.map(user => ({
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin,
        }));
    }),
});
//# sourceMappingURL=users.js.map