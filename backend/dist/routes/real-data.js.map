{"version": 3, "file": "real-data.js", "sourceRoot": "", "sources": ["../../src/routes/real-data.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,gBAAgB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAE9D,gBAAgB;AAChB,MAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC;IACjC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;CACf,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,CAAC,CAAC,MAAM,CAAC;IACrC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;IACd,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;CAC7C,CAAC,CAAC;AAEH,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;IACd,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;CACzC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC;IACjC,6BAA6B;IAC7B,MAAM,EAAE,eAAe;SACpB,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,eAAe,EAAE,CAAC;YACxD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,OAAO,EAAE,SAAS;oBAClB,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,uBAAuB;iBACnE;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B;aACvC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,sBAAsB;IACtB,UAAU,EAAE,eAAe;SACxB,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC9D,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;QACzB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,UAAU;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B;aAC5E,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,uBAAuB;IACvB,SAAS,EAAE,eAAe;SACvB,KAAK,CAAC,iBAAiB,CAAC;SACxB,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;QACzB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC7D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,iCAAiC;aACzC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,2CAA2C;IAC3C,eAAe,EAAE,eAAe;SAC7B,KAAK,CAAC,qBAAqB,CAAC;SAC5B,QAAQ,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;QAC5B,IAAI,CAAC;YACH,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,QAAQ,KAAK,CAAC,MAAM,EAAE,CAAC;gBACrB,KAAK,OAAO;oBACV,MAAM,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACtD,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,GAAG,MAAM,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBACxD,MAAM;YACV,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,WAAW,EAAE,KAAK,CAAC,EAAE;oBACrB,MAAM;iBACP;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,aAAa,KAAK,CAAC,MAAM,YAAY;aAC7C,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,qBAAqB;IACrB,IAAI,EAAE,eAAe;SAClB,KAAK,CAAC,mBAAmB,CAAC;SAC1B,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;QACzB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACxE,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,WAAW,EAAE,KAAK,CAAC,EAAE;oBACrB,IAAI;oBACJ,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B;aACtC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;CACL,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,gBAAgB,GAAG,MAAM,CAAC;IACrC,6BAA6B;IAC7B,OAAO,EAAE,eAAe;SACrB,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B;aACtC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,qDAAqD;IACrD,MAAM,EAAE,eAAe;SACpB,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,eAAe,EAAE,CAAC;YAE5D,2BAA2B;YAC3B,MAAM,eAAe,GAAG;gBACtB,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,EAAE,uBAAuB,EAAE;gBACpD,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,uBAAuB,EAAE;gBAC/C,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,yCAAyC,EAAE;gBACjE,8BAA8B;aAC/B,CAAC;YAEF,MAAM,mBAAmB,GAAG,MAAM,aAAa,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAEpF,4CAA4C;YAC5C,MAAM,WAAW,GAAG;gBAClB;oBACE,IAAI,EAAE,QAAQ;oBACd,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;oBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,2BAA2B;iBAC/D;gBACD,GAAG,mBAAmB;aACvB,CAAC;YAEF,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;YAElF,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW;oBAChD,QAAQ,EAAE,WAAW;oBACrB,MAAM,EAAE;wBACN,OAAO,EAAE,aAAa;qBACvB;oBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,+BAA+B;aACvC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,wBAAwB;IACxB,SAAS,EAAE,eAAe;SACvB,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,cAAc,EAAE,CAAC;YACvD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B;aACpC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;IAEJ,gCAAgC;IAChC,KAAK,EAAE,eAAe;SACnB,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACvD,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,eAAe,EAAE,CAAC;YAE5D,IAAI,cAAc,GAAG,CAAC,CAAC;YACvB,IAAI,iBAAiB,GAAG,CAAC,CAAC;YAE1B,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC5D,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC;oBACnC,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;gBAC5E,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,MAAM,EAAE,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;wBACzD,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;wBACjC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ;wBACjC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI;qBAC1B;oBACD,SAAS,EAAE;wBACT,GAAG,EAAE;4BACH,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK;4BACxB,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK;4BACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;yBACrC;wBACD,MAAM,EAAE;4BACN,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;4BACpD,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;4BACtD,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU;yBACtC;wBACD,IAAI,EAAE;4BACJ,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;4BAClD,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;4BACpD,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU;yBACpC;wBACD,OAAO,EAAE;4BACP,EAAE,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;4BACtD,GAAG,EAAE,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;yBACzD;qBACF;oBACD,UAAU,EAAE;wBACV,KAAK,EAAE,cAAc;wBACrB,OAAO,EAAE,iBAAiB;wBAC1B,OAAO,EAAE,cAAc,GAAG,iBAAiB;qBAC5C;oBACD,MAAM,EAAE;wBACN,OAAO,EAAE,aAAa;qBACvB;iBACF;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,4BAA4B;aACpC,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;CACL,CAAC,CAAC;AAEH,kBAAkB;AAClB,MAAM,CAAC,MAAM,cAAc,GAAG,MAAM,CAAC;IACnC,MAAM,EAAE,YAAY;IACpB,MAAM,EAAE,gBAAgB;CACzB,CAAC,CAAC"}