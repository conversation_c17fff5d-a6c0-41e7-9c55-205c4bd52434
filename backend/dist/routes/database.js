import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { db } from '../db/index.js';
import { logger } from '../utils/logger.js';
import { sql } from 'drizzle-orm';
// Define allowed tables and their valid columns for security
const ALLOWED_TABLES = {
    users: ['id', 'email', 'username', 'first_name', 'last_name', 'avatar', 'bio', 'role', 'is_active', 'is_verified', 'last_login', 'preferences', 'created_at', 'updated_at'],
    sessions: ['id', 'user_id', 'expires_at', 'created_at'],
    ai_models: ['id', 'name', 'type', 'version', 'status', 'accuracy', 'last_trained', 'dataset_size', 'framework', 'size', 'parameters', 'description', 'docker_image', 'container_id', 'model_path', 'config', 'created_at', 'updated_at'],
    training_jobs: ['id', 'model_id', 'status', 'progress', 'start_time', 'end_time', 'epochs', 'current_epoch', 'loss', 'accuracy', 'batch_size', 'learning_rate', 'dataset_path', 'config', 'logs', 'created_at', 'updated_at'],
    model_metrics: ['id', 'model_id', 'accuracy', 'precision', 'recall', 'f1_score', 'latency', 'throughput', 'memory_usage', 'cpu_usage', 'timestamp'],
};
// Helper function to validate table names
function isValidTable(tableName) {
    return tableName in ALLOWED_TABLES;
}
// Helper function to validate column names for a given table
function isValidColumn(tableName, columnName) {
    return ALLOWED_TABLES[tableName].includes(columnName);
}
// Helper function to sanitize order direction
function sanitizeOrderDirection(direction) {
    return direction.toLowerCase() === 'desc' ? 'DESC' : 'ASC';
}
// Schema for generic query execution
const QueryExecutionSchema = z.object({
    query: z.string().min(1),
    params: z.array(z.any()).optional(),
});
// Schema for table operations
const TableOperationSchema = z.object({
    tableName: z.string().min(1).refine(isValidTable, {
        message: "Invalid table name. Allowed tables: " + Object.keys(ALLOWED_TABLES).join(", ")
    }),
    limit: z.number().min(1).max(1000).default(100),
    offset: z.number().min(0).default(0),
    orderBy: z.string().optional().refine((val) => {
        if (!val)
            return true;
        // We'll validate this against table columns in the handler
        return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(val);
    }, {
        message: "Invalid column name format"
    }),
    orderDirection: z.enum(['asc', 'desc']).default('asc'),
    filters: z.record(z.any()).optional(),
});
// Schema for database connection testing
const ConnectionTestSchema = z.object({
    host: z.string().optional(),
    port: z.number().optional(),
    database: z.string().optional(),
    username: z.string().optional(),
    password: z.string().optional(),
});
export const databaseRouter = router({
    // Connection and health
    health: publicProcedure.query(async () => {
        try {
            // Test database connection with a simple query
            const result = await db.execute(sql `SELECT 1 as test`);
            return {
                status: 'healthy',
                connected: true,
                timestamp: new Date().toISOString(),
                testQuery: result.length > 0,
            };
        }
        catch (error) {
            logger.error('Database health check failed:', error);
            return {
                status: 'unhealthy',
                connected: false,
                timestamp: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }),
    testConnection: publicProcedure
        .input(ConnectionTestSchema)
        .mutation(async ({ input }) => {
        try {
            // For security, we won't actually use the provided credentials
            // This is just a placeholder for testing the current connection
            const result = await db.execute(sql `SELECT 1 as test`);
            return {
                success: true,
                message: 'Database connection successful',
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Database connection test failed:', error);
            throw new Error(`Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Schema introspection - Return only allowed tables for security
    getTables: publicProcedure.query(async () => {
        try {
            // Return only allowed tables instead of querying information_schema
            const allowedTableNames = Object.keys(ALLOWED_TABLES);
            // Optionally verify these tables exist in the database
            const tableExists = await Promise.all(allowedTableNames.map(async (tableName) => {
                try {
                    const result = await db.execute(sql `
              SELECT table_name
              FROM information_schema.tables 
              WHERE table_name = ${tableName}
              AND table_schema = current_schema()
            `);
                    return result.length > 0 ? { name: tableName, exists: true } : null;
                }
                catch {
                    return null;
                }
            }));
            const validTables = tableExists
                .filter((table) => table !== null)
                .map((table) => ({
                name: table.name,
                type: 'BASE TABLE',
                schema: 'public',
            }));
            return {
                success: true,
                tables: validTables,
            };
        }
        catch (error) {
            logger.error('Failed to get tables:', error);
            throw new Error(`Failed to get tables: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getTableSchema: publicProcedure
        .input(z.object({
        tableName: z.string().refine(isValidTable, {
            message: "Invalid table name. Allowed tables: " + Object.keys(ALLOWED_TABLES).join(", ")
        })
    }))
        .query(async ({ input }) => {
        try {
            const tableName = input.tableName;
            // Query to get column information for the validated table
            const result = await db.execute(sql `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale
          FROM information_schema.columns 
          WHERE table_name = ${input.tableName}
          AND table_schema = current_schema()
          ORDER BY ordinal_position
        `);
            return {
                success: true,
                tableName: input.tableName,
                columns: result.map((row) => ({
                    name: row.column_name,
                    type: row.data_type,
                    nullable: row.is_nullable === 'YES',
                    default: row.column_default,
                    maxLength: row.character_maximum_length,
                    precision: row.numeric_precision,
                    scale: row.numeric_scale,
                })),
            };
        }
        catch (error) {
            logger.error('Failed to get table schema:', error);
            throw new Error(`Failed to get table schema: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getTableIndexes: publicProcedure
        .input(z.object({ tableName: z.string() }))
        .query(async ({ input }) => {
        try {
            // Query to get index information for a specific table
            const result = await db.execute(sql `
          SELECT 
            i.relname as index_name,
            a.attname as column_name,
            ix.indisunique as is_unique,
            ix.indisprimary as is_primary
          FROM pg_class t
          JOIN pg_index ix ON t.oid = ix.indrelid
          JOIN pg_class i ON i.oid = ix.indexrelid
          JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
          WHERE t.relname = ${input.tableName}
          ORDER BY i.relname, a.attnum
        `);
            return {
                success: true,
                tableName: input.tableName,
                indexes: result.map((row) => ({
                    name: row.index_name,
                    column: row.column_name,
                    unique: row.is_unique,
                    primary: row.is_primary,
                })),
            };
        }
        catch (error) {
            logger.error('Failed to get table indexes:', error);
            throw new Error(`Failed to get table indexes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Query execution - Secure implementation with query validation
    executeQuery: publicProcedure
        .input(QueryExecutionSchema)
        .mutation(async ({ input }) => {
        try {
            // Strict security: only allow very basic SELECT queries for security
            const trimmedQuery = input.query.trim().toLowerCase();
            // Allow only SELECT statements
            if (!trimmedQuery.startsWith('select')) {
                throw new Error('Only SELECT queries are allowed for security reasons');
            }
            // Additional security checks
            const dangerousKeywords = [
                'drop', 'delete', 'update', 'insert', 'alter', 'create', 'truncate',
                'exec', 'execute', 'call', 'merge', 'replace', 'grant', 'revoke',
                ';', '--', '/*', '*/', 'xp_', 'sp_'
            ];
            for (const keyword of dangerousKeywords) {
                if (trimmedQuery.includes(keyword)) {
                    throw new Error(`Query contains prohibited keyword: ${keyword}`);
                }
            }
            // Limit query length to prevent abuse
            if (input.query.length > 1000) {
                throw new Error('Query too long. Maximum 1000 characters allowed.');
            }
            const startTime = Date.now();
            const result = await db.execute(sql.raw(input.query));
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                query: input.query,
                rows: result,
                rowCount: result.length,
                executionTime,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Query execution failed:', error);
            throw new Error(`Query execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Table data operations - Secure implementation with validated identifiers
    getTableData: publicProcedure
        .input(TableOperationSchema)
        .query(async ({ input }) => {
        try {
            const tableName = input.tableName;
            // Validate table name (already done by schema, but double-check)
            if (!isValidTable(tableName)) {
                throw new Error(`Invalid table name: ${tableName}`);
            }
            // Validate orderBy column if provided
            if (input.orderBy && !isValidColumn(tableName, input.orderBy)) {
                throw new Error(`Invalid column name: ${input.orderBy} for table: ${tableName}`);
            }
            // Build secure query with SQL template strings
            let baseQuery = sql `SELECT * FROM ${sql.identifier(tableName)}`;
            let whereConditions = [];
            // Add filters if provided (validate column names)
            if (input.filters && Object.keys(input.filters).length > 0) {
                const validFilters = Object.entries(input.filters)
                    .filter(([key, value]) => {
                    if (value === undefined || value === null || value === '')
                        return false;
                    if (!isValidColumn(tableName, key)) {
                        logger.warn(`Invalid filter column: ${key} for table: ${tableName}`);
                        return false;
                    }
                    return true;
                });
                whereConditions = validFilters.map(([key, value]) => sql `${sql.identifier(key)} ILIKE ${`%${value}%`}`);
            }
            // Build the final query
            let finalQuery = baseQuery;
            if (whereConditions.length > 0) {
                finalQuery = sql `${finalQuery} WHERE ${sql.join(whereConditions, sql ` AND `)}`;
            }
            // Add ordering (column name already validated)
            if (input.orderBy) {
                const orderDirection = sanitizeOrderDirection(input.orderDirection);
                finalQuery = sql `${finalQuery} ORDER BY ${sql.identifier(input.orderBy)} ${sql.raw(orderDirection)}`;
            }
            // Add pagination
            finalQuery = sql `${finalQuery} LIMIT ${input.limit} OFFSET ${input.offset}`;
            const startTime = Date.now();
            const result = await db.execute(finalQuery);
            const executionTime = Date.now() - startTime;
            // Get total count for pagination using secure query
            const countQuery = sql `SELECT COUNT(*) as count FROM ${sql.identifier(tableName)}`;
            const countResult = await db.execute(countQuery);
            const totalCount = parseInt(String(countResult[0]?.count || '0'));
            return {
                success: true,
                tableName: input.tableName,
                data: result,
                pagination: {
                    limit: input.limit,
                    offset: input.offset,
                    total: totalCount,
                    pages: Math.ceil(totalCount / input.limit),
                    currentPage: Math.floor(input.offset / input.limit) + 1,
                },
                executionTime,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get table data:', error);
            throw new Error(`Failed to get table data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Database statistics
    getDatabaseStats: publicProcedure.query(async () => {
        try {
            // Get database size and table statistics
            const sizeResult = await db.execute(sql `
        SELECT 
          pg_size_pretty(pg_database_size(current_database())) as database_size,
          current_database() as database_name
      `);
            const tableStatsResult = await db.execute(sql `
        SELECT 
          schemaname,
          relname as tablename,
          n_tup_ins as inserts,
          n_tup_upd as updates,
          n_tup_del as deletes,
          n_live_tup as live_tuples,
          n_dead_tup as dead_tuples
        FROM pg_stat_user_tables
        ORDER BY n_live_tup DESC
      `);
            return {
                success: true,
                database: {
                    name: sizeResult[0]?.database_name,
                    size: sizeResult[0]?.database_size,
                },
                tables: tableStatsResult.map((row) => ({
                    schema: row.schemaname,
                    name: row.tablename,
                    stats: {
                        inserts: parseInt(row.inserts || '0'),
                        updates: parseInt(row.updates || '0'),
                        deletes: parseInt(row.deletes || '0'),
                        liveTuples: parseInt(row.live_tuples || '0'),
                        deadTuples: parseInt(row.dead_tuples || '0'),
                        lastVacuum: row.last_vacuum,
                        lastAutoVacuum: row.last_autovacuum,
                        lastAnalyze: row.last_analyze,
                        lastAutoAnalyze: row.last_autoanalyze,
                    },
                })),
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get database stats:', error);
            throw new Error(`Failed to get database stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Active connections and queries
    getActiveConnections: publicProcedure.query(async () => {
        try {
            const result = await db.execute(sql `
        SELECT 
          pid,
          usename as username,
          application_name,
          client_addr as client_address,
          client_port,
          backend_start,
          state,
          query_start,
          query
        FROM pg_stat_activity
        WHERE state != 'idle'
        ORDER BY backend_start DESC
      `);
            return {
                success: true,
                connections: result.map((row) => ({
                    pid: row.pid,
                    username: row.username,
                    applicationName: row.application_name,
                    clientAddress: row.client_address,
                    clientPort: row.client_port,
                    backendStart: row.backend_start,
                    state: row.state,
                    queryStart: row.query_start,
                    query: row.query,
                })),
                count: result.length,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get active connections:', error);
            throw new Error(`Failed to get active connections: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
});
//# sourceMappingURL=database.js.map