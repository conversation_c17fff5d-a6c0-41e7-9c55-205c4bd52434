import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { dockerService } from '../services/docker.js';
import { systemMonitor } from '../services/system-monitor.js';
// Input schemas
const containerIdSchema = z.object({
    id: z.string()
});
const containerActionSchema = z.object({
    id: z.string(),
    action: z.enum(['start', 'stop', 'restart'])
});
const containerLogsSchema = z.object({
    id: z.string(),
    tail: z.number().optional().default(100)
});
export const dockerRouter = router({
    // Check if Docker is running
    status: publicProcedure
        .query(async () => {
        try {
            const isRunning = await dockerService.isDockerRunning();
            return {
                success: true,
                data: {
                    running: isRunning,
                    message: isRunning ? 'Docker is running' : 'Docker is not running'
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to check Docker status'
            };
        }
    }),
    // List all containers
    containers: publicProcedure
        .input(z.object({ all: z.boolean().optional().default(true) }))
        .query(async ({ input }) => {
        try {
            const containers = await dockerService.listContainers(input.all);
            return {
                success: true,
                data: containers
            };
        }
        catch (error) {
            console.error('Docker containers error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to list containers'
            };
        }
    }),
    // Get single container
    container: publicProcedure
        .input(containerIdSchema)
        .query(async ({ input }) => {
        try {
            const container = await dockerService.getContainer(input.id);
            return {
                success: true,
                data: container
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to get container details'
            };
        }
    }),
    // Container actions (start, stop, restart)
    containerAction: publicProcedure
        .input(containerActionSchema)
        .mutation(async ({ input }) => {
        try {
            let result = false;
            switch (input.action) {
                case 'start':
                    result = await dockerService.startContainer(input.id);
                    break;
                case 'stop':
                    result = await dockerService.stopContainer(input.id);
                    break;
                case 'restart':
                    result = await dockerService.restartContainer(input.id);
                    break;
            }
            return {
                success: result,
                data: {
                    action: input.action,
                    containerId: input.id,
                    result
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: `Failed to ${input.action} container`
            };
        }
    }),
    // Get container logs
    logs: publicProcedure
        .input(containerLogsSchema)
        .query(async ({ input }) => {
        try {
            const logs = await dockerService.getContainerLogs(input.id, input.tail);
            return {
                success: true,
                data: {
                    containerId: input.id,
                    logs,
                    tail: input.tail
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to get container logs'
            };
        }
    })
});
export const systemRouterReal = router({
    // Get current system metrics
    metrics: publicProcedure
        .query(async () => {
        try {
            const metrics = await systemMonitor.getSystemMetrics();
            return {
                success: true,
                data: metrics
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to get system metrics'
            };
        }
    }),
    // Enhanced health check with real service monitoring
    health: publicProcedure
        .query(async () => {
        try {
            // Check Docker status
            const dockerRunning = await dockerService.isDockerRunning();
            // Define services to check
            const servicesToCheck = [
                { name: 'PostgreSQL', url: 'http://localhost:5432' },
                { name: 'Redis', url: 'http://localhost:6379' },
                { name: 'MinIO', url: 'http://localhost:9000/minio/health/live' },
                // Add more services as needed
            ];
            const serviceHealthChecks = await systemMonitor.checkServiceHealth(servicesToCheck);
            // Combine Docker status with service health
            const allServices = [
                {
                    name: 'Docker',
                    status: dockerRunning ? 'healthy' : 'unhealthy',
                    lastCheck: new Date().toISOString(),
                    error: dockerRunning ? undefined : 'Docker daemon not running'
                },
                ...serviceHealthChecks
            ];
            const overallHealthy = allServices.every(service => service.status === 'healthy');
            return {
                success: true,
                data: {
                    status: overallHealthy ? 'healthy' : 'unhealthy',
                    services: allServices,
                    docker: {
                        running: dockerRunning
                    },
                    timestamp: new Date().toISOString()
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to check system health'
            };
        }
    }),
    // Get running processes
    processes: publicProcedure
        .query(async () => {
        try {
            const processes = await systemMonitor.getProcessList();
            return {
                success: true,
                data: processes
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to get process list'
            };
        }
    }),
    // Enhanced stats with real data
    stats: publicProcedure
        .query(async () => {
        try {
            const metrics = await systemMonitor.getSystemMetrics();
            const dockerRunning = await dockerService.isDockerRunning();
            let containerCount = 0;
            let runningContainers = 0;
            if (dockerRunning) {
                try {
                    const containers = await dockerService.listContainers(true);
                    containerCount = containers.length;
                    runningContainers = containers.filter(c => c.status === 'running').length;
                }
                catch (error) {
                    console.error('Error getting container stats:', error);
                }
            }
            return {
                success: true,
                data: {
                    system: {
                        uptime: systemMonitor.formatUptime(metrics.system.uptime),
                        hostname: metrics.system.hostname,
                        platform: metrics.system.platform,
                        arch: metrics.system.arch
                    },
                    resources: {
                        cpu: {
                            usage: metrics.cpu.usage,
                            cores: metrics.cpu.cores,
                            temperature: metrics.cpu.temperature
                        },
                        memory: {
                            used: systemMonitor.formatBytes(metrics.memory.used),
                            total: systemMonitor.formatBytes(metrics.memory.total),
                            percentage: metrics.memory.percentage
                        },
                        disk: {
                            used: systemMonitor.formatBytes(metrics.disk.used),
                            total: systemMonitor.formatBytes(metrics.disk.total),
                            percentage: metrics.disk.percentage
                        },
                        network: {
                            in: systemMonitor.formatBytes(metrics.network.bytesIn),
                            out: systemMonitor.formatBytes(metrics.network.bytesOut)
                        }
                    },
                    containers: {
                        total: containerCount,
                        running: runningContainers,
                        stopped: containerCount - runningContainers
                    },
                    docker: {
                        running: dockerRunning
                    }
                }
            };
        }
        catch (error) {
            return {
                success: false,
                error: 'Failed to get system stats'
            };
        }
    })
});
// Combined router
export const realDataRouter = router({
    docker: dockerRouter,
    system: systemRouterReal
});
//# sourceMappingURL=real-data.js.map