import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { dockerService } from '../services/docker.js';
import { logger } from '../utils/logger.js';
export const dockerRouter = router({
    // Docker daemon health
    isRunning: publicProcedure.query(async () => {
        try {
            const isRunning = await dockerService.isDockerRunning();
            return {
                running: isRunning,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Docker health check failed:', error);
            return {
                running: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString(),
            };
        }
    }),
    // Container management
    listContainers: publicProcedure
        .input(z.object({ all: z.boolean().default(true) }).optional())
        .query(async ({ input }) => {
        try {
            const containers = await dockerService.listContainers(input?.all ?? true);
            return {
                success: true,
                containers,
                count: containers.length,
            };
        }
        catch (error) {
            logger.error('Failed to list containers:', error);
            throw new Error(`Failed to list containers: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getContainer: publicProcedure
        .input(z.object({ id: z.string() }))
        .query(async ({ input }) => {
        try {
            const container = await dockerService.getContainer(input.id);
            if (!container) {
                throw new Error(`Container ${input.id} not found`);
            }
            return {
                success: true,
                container,
            };
        }
        catch (error) {
            logger.error('Failed to get container:', error);
            throw new Error(`Failed to get container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    startContainer: publicProcedure
        .input(z.object({ id: z.string() }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.startContainer(input.id);
            if (!success) {
                throw new Error(`Failed to start container ${input.id}`);
            }
            return {
                success: true,
                message: `Container ${input.id} started successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to start container:', error);
            throw new Error(`Failed to start container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    stopContainer: publicProcedure
        .input(z.object({ id: z.string() }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.stopContainer(input.id);
            if (!success) {
                throw new Error(`Failed to stop container ${input.id}`);
            }
            return {
                success: true,
                message: `Container ${input.id} stopped successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to stop container:', error);
            throw new Error(`Failed to stop container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    restartContainer: publicProcedure
        .input(z.object({ id: z.string() }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.restartContainer(input.id);
            if (!success) {
                throw new Error(`Failed to restart container ${input.id}`);
            }
            return {
                success: true,
                message: `Container ${input.id} restarted successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to restart container:', error);
            throw new Error(`Failed to restart container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    removeContainer: publicProcedure
        .input(z.object({ id: z.string() }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.removeContainer(input.id);
            if (!success) {
                throw new Error(`Failed to remove container ${input.id}`);
            }
            return {
                success: true,
                message: `Container ${input.id} removed successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to remove container:', error);
            throw new Error(`Failed to remove container: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getContainerLogs: publicProcedure
        .input(z.object({
        id: z.string(),
        tail: z.number().min(1).max(10000).default(100),
    }))
        .query(async ({ input }) => {
        try {
            const logs = await dockerService.getContainerLogs(input.id, input.tail);
            return {
                success: true,
                logs,
                containerId: input.id,
                lines: input.tail,
            };
        }
        catch (error) {
            logger.error('Failed to get container logs:', error);
            throw new Error(`Failed to get container logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Bulk operations
    startMultipleContainers: publicProcedure
        .input(z.object({ ids: z.array(z.string()) }))
        .mutation(async ({ input }) => {
        try {
            const results = await Promise.allSettled(input.ids.map(id => dockerService.startContainer(id)));
            const successful = results.filter((result, index) => result.status === 'fulfilled' && result.value).length;
            const failed = results.length - successful;
            return {
                success: successful > 0,
                message: `Started ${successful} containers${failed > 0 ? `, ${failed} failed` : ''}`,
                successful,
                failed,
                total: results.length,
            };
        }
        catch (error) {
            logger.error('Failed to start multiple containers:', error);
            throw new Error(`Failed to start containers: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    stopMultipleContainers: publicProcedure
        .input(z.object({ ids: z.array(z.string()) }))
        .mutation(async ({ input }) => {
        try {
            const results = await Promise.allSettled(input.ids.map(id => dockerService.stopContainer(id)));
            const successful = results.filter((result, index) => result.status === 'fulfilled' && result.value).length;
            const failed = results.length - successful;
            return {
                success: successful > 0,
                message: `Stopped ${successful} containers${failed > 0 ? `, ${failed} failed` : ''}`,
                successful,
                failed,
                total: results.length,
            };
        }
        catch (error) {
            logger.error('Failed to stop multiple containers:', error);
            throw new Error(`Failed to stop containers: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Docker Compose operations
    composeUp: publicProcedure
        .input(z.object({
        detached: z.boolean().default(true),
        build: z.boolean().default(false),
        recreate: z.boolean().default(false),
        services: z.array(z.string()).optional(),
    }).optional())
        .mutation(async ({ input }) => {
        try {
            const output = await dockerService.executeDockerCompose('up', input || {});
            return {
                success: true,
                message: 'Docker Compose up executed successfully',
                output,
                options: input,
            };
        }
        catch (error) {
            logger.error('Failed to execute docker compose up:', error);
            throw new Error(`Failed to execute docker compose up: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    composeDown: publicProcedure
        .input(z.object({
        removeVolumes: z.boolean().default(false),
        removeImages: z.boolean().default(false),
        removeOrphans: z.boolean().default(false),
    }).optional())
        .mutation(async ({ input }) => {
        try {
            const output = await dockerService.executeDockerCompose('down', input || {});
            return {
                success: true,
                message: 'Docker Compose down executed successfully',
                output,
                options: input,
            };
        }
        catch (error) {
            logger.error('Failed to execute docker compose down:', error);
            throw new Error(`Failed to execute docker compose down: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    composeBuild: publicProcedure
        .input(z.object({
        service: z.string().optional(),
        noCache: z.boolean().default(false),
    }).optional())
        .mutation(async ({ input }) => {
        try {
            const output = await dockerService.buildService(input?.service, { noCache: input?.noCache });
            return {
                success: true,
                message: 'Docker Compose build completed successfully',
                output,
            };
        }
        catch (error) {
            logger.error('Failed to build Docker Compose services:', error);
            throw new Error(`Failed to build services: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    composePull: publicProcedure
        .input(z.object({
        service: z.string().optional(),
    }).optional())
        .mutation(async ({ input }) => {
        try {
            const output = await dockerService.pullImages(input?.service);
            return {
                success: true,
                message: 'Docker Compose pull completed successfully',
                output,
            };
        }
        catch (error) {
            logger.error('Failed to pull Docker Compose images:', error);
            throw new Error(`Failed to pull images: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getComposeStatus: publicProcedure.query(async () => {
        try {
            const status = await dockerService.getComposeStatus();
            return {
                success: true,
                status,
            };
        }
        catch (error) {
            logger.error('Failed to get Docker Compose status:', error);
            throw new Error(`Failed to get Docker Compose status: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getComposeServices: publicProcedure.query(async () => {
        try {
            const services = await dockerService.getDockerComposeServices();
            return {
                success: true,
                services,
            };
        }
        catch (error) {
            logger.error('Failed to get Docker Compose services:', error);
            throw new Error(`Failed to get Docker Compose services: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Image management
    getImages: publicProcedure.query(async () => {
        try {
            const images = await dockerService.getImages();
            return {
                success: true,
                images,
                count: images.length,
            };
        }
        catch (error) {
            logger.error('Failed to get Docker images:', error);
            throw new Error(`Failed to get Docker images: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    removeImage: publicProcedure
        .input(z.object({
        id: z.string(),
        force: z.boolean().default(false),
    }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.removeImage(input.id, input.force);
            if (!success) {
                throw new Error(`Failed to remove image ${input.id}`);
            }
            return {
                success: true,
                message: `Image ${input.id} removed successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to remove Docker image:', error);
            throw new Error(`Failed to remove image: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Network management
    getNetworks: publicProcedure.query(async () => {
        try {
            const networks = await dockerService.getNetworks();
            return {
                success: true,
                networks,
                count: networks.length,
            };
        }
        catch (error) {
            logger.error('Failed to get Docker networks:', error);
            throw new Error(`Failed to get Docker networks: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Volume management
    getVolumes: publicProcedure.query(async () => {
        try {
            const volumes = await dockerService.getVolumes();
            return {
                success: true,
                volumes,
                count: volumes.length,
            };
        }
        catch (error) {
            logger.error('Failed to get Docker volumes:', error);
            throw new Error(`Failed to get Docker volumes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    removeVolume: publicProcedure
        .input(z.object({
        name: z.string(),
        force: z.boolean().default(false),
    }))
        .mutation(async ({ input }) => {
        try {
            const success = await dockerService.removeVolume(input.name, input.force);
            if (!success) {
                throw new Error(`Failed to remove volume ${input.name}`);
            }
            return {
                success: true,
                message: `Volume ${input.name} removed successfully`,
            };
        }
        catch (error) {
            logger.error('Failed to remove Docker volume:', error);
            throw new Error(`Failed to remove volume: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Container statistics
    getContainerStats: publicProcedure
        .input(z.object({ id: z.string() }))
        .query(async ({ input }) => {
        try {
            const container = await dockerService.getContainer(input.id);
            if (!container) {
                throw new Error(`Container ${input.id} not found`);
            }
            return {
                success: true,
                stats: container.stats,
                containerId: input.id,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get container stats:', error);
            throw new Error(`Failed to get container stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // System-wide Docker info
    getSystemInfo: publicProcedure.query(async () => {
        try {
            const isRunning = await dockerService.isDockerRunning();
            const containers = await dockerService.listContainers(true);
            const df = await dockerService.getSystemDf();
            const runningContainers = containers.filter(c => c.status === 'running').length;
            const stoppedContainers = containers.filter(c => c.status === 'stopped').length;
            return {
                success: true,
                info: {
                    dockerRunning: isRunning,
                    totalContainers: containers.length,
                    runningContainers,
                    stoppedContainers,
                    systemDf: df,
                    timestamp: new Date().toISOString(),
                },
            };
        }
        catch (error) {
            logger.error('Failed to get Docker system info:', error);
            throw new Error(`Failed to get Docker system info: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getSystemDf: publicProcedure.query(async () => {
        try {
            const df = await dockerService.getSystemDf();
            return {
                success: true,
                df,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get Docker system df:', error);
            throw new Error(`Failed to get Docker system df: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
});
//# sourceMappingURL=docker.js.map