import { router } from '../lib/trpc.js';
import { userRouter } from './users.js';
import { authRouter } from './auth.js';
import { systemRouter } from './system.js';
import { realDataRouter } from './real-data.js';
import { vectorRouter } from './vector.js';
import { dockerRouter } from './docker.js';
import { databaseRouter } from './database.js';
import { etcdRouter } from './etcd.js';
import { modelsRouter } from './models.js';
import { filesRouter } from './files.js';
import { postsRouter } from './posts.js';
import { logsRouter } from './logs.js';
import { settingsRouter } from './settings.js';
// Main app router - combine all route modules
export const appRouter = router({
    auth: authRouter,
    users: userRouter,
    system: systemRouter,
    realData: realDataRouter,
    vector: vectorRouter,
    docker: dockerRouter,
    database: databaseRouter,
    etcd: etcdRouter,
    models: modelsRouter,
    files: filesRouter,
    posts: postsRouter,
    logs: logsRouter,
    settings: settingsRouter,
    // Add more routers here as you create them
    // achievements: achievementRouter,
    // lighthouse: lighthouseRouter,
});
//# sourceMappingURL=index.js.map