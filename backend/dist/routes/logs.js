import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
// Input schemas
const getLogsSchema = z.object({
    limit: z.number().min(1).max(1000).optional().default(100),
    offset: z.number().min(0).optional().default(0),
    level: z.enum(['info', 'warn', 'error', 'debug']).optional(),
    service: z.string().optional(),
    search: z.string().optional()
});
// In-memory logs storage (in production, this would be from a logging service)
let logs = [
    {
        id: '1',
        timestamp: new Date().toISOString(),
        level: 'info',
        service: 'api',
        message: 'Server started on port 4001',
    },
    {
        id: '2',
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'warn',
        service: 'postgres',
        message: 'Connection pool reaching limit',
        data: { poolSize: 18, maxSize: 20 }
    },
    {
        id: '3',
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'error',
        service: 'redis',
        message: 'Connection timeout',
        data: { timeout: 5000, attempt: 3 }
    },
    {
        id: '4',
        timestamp: new Date(Date.now() - 180000).toISOString(),
        level: 'info',
        service: 'milvus',
        message: 'Vector index created successfully',
        data: { collection: 'embeddings', vectors: 1000 }
    },
    {
        id: '5',
        timestamp: new Date(Date.now() - 240000).toISOString(),
        level: 'info',
        service: 'minio',
        message: 'File uploaded successfully',
        data: { bucket: 'file-storage', key: 'uploads/document.pdf', size: 1024576 }
    },
    {
        id: '6',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        level: 'debug',
        service: 'auth',
        message: 'User authentication attempt',
        data: { userId: 'user123', method: 'jwt' }
    }
];
// Function to generate new log entries periodically
function generateLogEntry() {
    const services = ['api', 'postgres', 'redis', 'milvus', 'minio', 'auth', 'docker'];
    const levels = ['info', 'warn', 'error', 'debug'];
    const messages = {
        api: ['Request processed successfully', 'API endpoint called', 'Rate limit applied'],
        postgres: ['Query executed', 'Connection established', 'Transaction committed'],
        redis: ['Cache hit', 'Cache miss', 'Key expired'],
        milvus: ['Vector search completed', 'Index updated', 'Collection created'],
        minio: ['File uploaded', 'File downloaded', 'Bucket created'],
        auth: ['User login', 'Token validated', 'Session expired'],
        docker: ['Container started', 'Container stopped', 'Image pulled']
    };
    const service = services[Math.floor(Math.random() * services.length)];
    const level = levels[Math.floor(Math.random() * levels.length)];
    const serviceMessages = messages[service];
    const message = serviceMessages[Math.floor(Math.random() * serviceMessages.length)];
    return {
        id: `${Date.now()}-${Math.random()}`,
        timestamp: new Date().toISOString(),
        level,
        service,
        message,
        data: Math.random() > 0.5 ? {
            requestId: `req-${Math.random().toString(36).substr(2, 9)}`,
            duration: Math.floor(Math.random() * 1000),
            ip: `192.168.1.${Math.floor(Math.random() * 255)}`
        } : undefined
    };
}
// Generate new logs periodically
setInterval(() => {
    if (logs.length > 500) {
        logs = logs.slice(-400); // Keep only last 400 logs
    }
    logs.push(generateLogEntry());
}, 5000); // Add new log every 5 seconds
export const logsRouter = router({
    // Get logs with filtering and pagination
    list: publicProcedure
        .input(getLogsSchema)
        .query(async ({ input }) => {
        let filteredLogs = [...logs];
        // Apply filters
        if (input.level) {
            filteredLogs = filteredLogs.filter(log => log.level === input.level);
        }
        if (input.service) {
            filteredLogs = filteredLogs.filter(log => log.service === input.service);
        }
        if (input.search) {
            const searchTerm = input.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => log.message.toLowerCase().includes(searchTerm) ||
                log.service.toLowerCase().includes(searchTerm));
        }
        // Sort by timestamp (newest first)
        filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        // Apply pagination
        const total = filteredLogs.length;
        const paginatedLogs = filteredLogs.slice(input.offset, input.offset + input.limit);
        return {
            logs: paginatedLogs,
            total,
            hasMore: input.offset + input.limit < total
        };
    }),
    // Get log statistics
    stats: publicProcedure
        .query(async () => {
        const now = new Date();
        const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const lastHour = new Date(now.getTime() - 60 * 60 * 1000);
        const recentLogs = logs.filter(log => new Date(log.timestamp) >= last24Hours);
        const lastHourLogs = logs.filter(log => new Date(log.timestamp) >= lastHour);
        return {
            total: logs.length,
            last24Hours: recentLogs.length,
            lastHour: lastHourLogs.length,
            errors: logs.filter(log => log.level === 'error').length,
            warnings: logs.filter(log => log.level === 'warn').length,
            services: Array.from(new Set(logs.map(log => log.service))),
            errorRate: logs.filter(log => log.level === 'error').length / Math.max(logs.length, 1),
            levels: {
                info: logs.filter(log => log.level === 'info').length,
                warn: logs.filter(log => log.level === 'warn').length,
                error: logs.filter(log => log.level === 'error').length,
                debug: logs.filter(log => log.level === 'debug').length,
            }
        };
    }),
    // Get available services
    services: publicProcedure
        .query(async () => {
        const services = Array.from(new Set(logs.map(log => log.service)));
        return services.sort();
    }),
    // Clear logs (admin function)
    clear: publicProcedure
        .mutation(async () => {
        const clearedCount = logs.length;
        logs = [];
        // Add a system log about clearing
        logs.push({
            id: `${Date.now()}-clear`,
            timestamp: new Date().toISOString(),
            level: 'info',
            service: 'system',
            message: `Logs cleared by admin. Removed ${clearedCount} log entries.`,
            data: { clearedCount }
        });
        return { success: true, clearedCount };
    })
});
//# sourceMappingURL=logs.js.map