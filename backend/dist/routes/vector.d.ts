export declare const vectorRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    health: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            status: string;
            connected: boolean;
            timestamp: string;
        };
    }>;
    connect: import("@trpc/server").TRPCMutationProcedure<{
        input: void;
        output: {
            success: boolean;
            message: string;
        };
    }>;
    listCollections: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: ({
            name: string;
            statistics: any;
            loaded: boolean;
            error?: undefined;
        } | {
            name: string;
            statistics: null;
            loaded: boolean;
            error: string;
        })[];
    }>;
    createCollection: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
            dimension: number;
            description?: string | undefined;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    dropCollection: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    hasCollection: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            name: string;
        };
        output: {
            exists: boolean;
        };
    }>;
    loadCollection: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    releaseCollection: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    createIndex: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            collectionName: string;
            params?: Record<string, any> | undefined;
            fieldName?: string | undefined;
            indexType?: string | undefined;
            metricType?: "L2" | "IP" | "COSINE" | undefined;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    insert: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            collectionName: string;
            documents: {
                id: string;
                vector: number[];
                metadata?: Record<string, any> | undefined;
            }[];
        };
        output: {
            success: boolean;
            message: string;
            count: number;
        };
    }>;
    search: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            collectionName: string;
            queryVectors: number[][];
            params?: Record<string, any> | undefined;
            topK?: number | undefined;
        };
        output: {
            success: boolean;
            results: import("../services/vector/milvus.js").VectorSearchResult[][];
            totalQueries: number;
        };
    }>;
    query: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            expr: string;
            collectionName: string;
            outputFields?: string[] | undefined;
        };
        output: {
            success: boolean;
            results: any[];
            count: number;
        };
    }>;
    delete: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            ids: string[];
            collectionName: string;
        };
        output: {
            success: boolean;
            message: string;
            count: number;
        };
    }>;
    getCollectionStatistics: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            name: string;
        };
        output: {
            success: boolean;
            statistics: any;
            collectionName: string;
        };
    }>;
    flush: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
}>;
//# sourceMappingURL=vector.d.ts.map