import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { milvusService } from '../services/vector/milvus.js';
import { logger } from '../utils/logger.js';
// Zod schemas for validation
const VectorDocumentSchema = z.object({
    id: z.string(),
    vector: z.array(z.number()),
    metadata: z.record(z.any()).optional(),
});
const SearchParamsSchema = z.object({
    collectionName: z.string(),
    queryVectors: z.array(z.array(z.number())),
    topK: z.number().min(1).max(1000).default(10),
    params: z.record(z.any()).optional(),
});
const CollectionCreateSchema = z.object({
    name: z.string().min(1),
    dimension: z.number().min(1).max(65536),
    description: z.string().optional(),
});
const IndexCreateSchema = z.object({
    collectionName: z.string(),
    fieldName: z.string().default('vector'),
    indexType: z.string().default('IVF_FLAT'),
    metricType: z.enum(['L2', 'IP', 'COSINE']).default('L2'),
    params: z.record(z.any()).optional(),
});
export const vectorRouter = router({
    // Health and connection
    health: publicProcedure.query(async () => {
        try {
            const isHealthy = await milvusService.checkHealth();
            return {
                status: isHealthy ? 'healthy' : 'unhealthy',
                connected: milvusService.isConnected(),
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Vector service health check failed:', error);
            throw new Error('Vector service health check failed');
        }
    }),
    connect: publicProcedure.mutation(async () => {
        try {
            await milvusService.connect();
            return { success: true, message: 'Connected to Milvus successfully' };
        }
        catch (error) {
            logger.error('Failed to connect to Milvus:', error);
            throw new Error('Failed to connect to vector database');
        }
    }),
    // Collection management
    listCollections: publicProcedure.query(async () => {
        try {
            const collections = await milvusService.listCollections();
            const collectionsWithStats = await Promise.all(collections.map(async (name) => {
                try {
                    const stats = await milvusService.getCollectionStatistics(name);
                    return {
                        name,
                        statistics: stats,
                        loaded: true, // Assume loaded for now
                    };
                }
                catch (error) {
                    return {
                        name,
                        statistics: null,
                        loaded: false,
                        error: error instanceof Error ? error.message : 'Unknown error',
                    };
                }
            }));
            return collectionsWithStats;
        }
        catch (error) {
            logger.error('Failed to list collections:', error);
            throw new Error('Failed to retrieve collections');
        }
    }),
    createCollection: publicProcedure
        .input(CollectionCreateSchema)
        .mutation(async ({ input }) => {
        try {
            await milvusService.createCollection(input.name, input.dimension, input.description);
            return { success: true, message: `Collection ${input.name} created successfully` };
        }
        catch (error) {
            logger.error('Failed to create collection:', error);
            throw new Error(`Failed to create collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    dropCollection: publicProcedure
        .input(z.object({ name: z.string() }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.dropCollection(input.name);
            return { success: true, message: `Collection ${input.name} dropped successfully` };
        }
        catch (error) {
            logger.error('Failed to drop collection:', error);
            throw new Error(`Failed to drop collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    hasCollection: publicProcedure
        .input(z.object({ name: z.string() }))
        .query(async ({ input }) => {
        try {
            const exists = await milvusService.hasCollection(input.name);
            return { exists };
        }
        catch (error) {
            logger.error('Failed to check collection existence:', error);
            throw new Error('Failed to check collection existence');
        }
    }),
    loadCollection: publicProcedure
        .input(z.object({ name: z.string() }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.loadCollection(input.name);
            return { success: true, message: `Collection ${input.name} loaded successfully` };
        }
        catch (error) {
            logger.error('Failed to load collection:', error);
            throw new Error(`Failed to load collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    releaseCollection: publicProcedure
        .input(z.object({ name: z.string() }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.releaseCollection(input.name);
            return { success: true, message: `Collection ${input.name} released successfully` };
        }
        catch (error) {
            logger.error('Failed to release collection:', error);
            throw new Error(`Failed to release collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Index management
    createIndex: publicProcedure
        .input(IndexCreateSchema)
        .mutation(async ({ input }) => {
        try {
            await milvusService.createIndex(input.collectionName, input.fieldName, input.indexType, input.metricType, input.params);
            return { success: true, message: `Index created for collection ${input.collectionName}` };
        }
        catch (error) {
            logger.error('Failed to create index:', error);
            throw new Error(`Failed to create index: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Data operations
    insert: publicProcedure
        .input(z.object({
        collectionName: z.string(),
        documents: z.array(VectorDocumentSchema),
    }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.insert(input.collectionName, input.documents);
            return {
                success: true,
                message: `Inserted ${input.documents.length} documents into ${input.collectionName}`,
                count: input.documents.length,
            };
        }
        catch (error) {
            logger.error('Failed to insert documents:', error);
            throw new Error(`Failed to insert documents: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    search: publicProcedure
        .input(SearchParamsSchema)
        .mutation(async ({ input }) => {
        try {
            const results = await milvusService.search(input.collectionName, input.queryVectors, input.topK, input.params);
            return {
                success: true,
                results,
                totalQueries: input.queryVectors.length,
            };
        }
        catch (error) {
            logger.error('Vector search failed:', error);
            throw new Error(`Vector search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    query: publicProcedure
        .input(z.object({
        collectionName: z.string(),
        expr: z.string(),
        outputFields: z.array(z.string()).optional(),
    }))
        .query(async ({ input }) => {
        try {
            const results = await milvusService.query(input.collectionName, input.expr, input.outputFields);
            return {
                success: true,
                results,
                count: results.length,
            };
        }
        catch (error) {
            logger.error('Vector query failed:', error);
            throw new Error(`Vector query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    delete: publicProcedure
        .input(z.object({
        collectionName: z.string(),
        ids: z.array(z.string()),
    }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.delete(input.collectionName, input.ids);
            return {
                success: true,
                message: `Deleted ${input.ids.length} documents from ${input.collectionName}`,
                count: input.ids.length,
            };
        }
        catch (error) {
            logger.error('Failed to delete documents:', error);
            throw new Error(`Failed to delete documents: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Statistics and utilities
    getCollectionStatistics: publicProcedure
        .input(z.object({ name: z.string() }))
        .query(async ({ input }) => {
        try {
            const stats = await milvusService.getCollectionStatistics(input.name);
            return {
                success: true,
                statistics: stats,
                collectionName: input.name,
            };
        }
        catch (error) {
            logger.error('Failed to get collection statistics:', error);
            throw new Error(`Failed to get collection statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    flush: publicProcedure
        .input(z.object({ name: z.string() }))
        .mutation(async ({ input }) => {
        try {
            await milvusService.flush(input.name);
            return { success: true, message: `Collection ${input.name} flushed successfully` };
        }
        catch (error) {
            logger.error('Failed to flush collection:', error);
            throw new Error(`Failed to flush collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
});
//# sourceMappingURL=vector.js.map