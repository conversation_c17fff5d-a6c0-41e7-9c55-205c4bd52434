export declare const modelsRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    getModels: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            models: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                description: string | null;
                type: "embedding" | "llm" | "classification" | "generation" | "other";
                version: string;
                status: "idle" | "loading" | "running" | "error";
                accuracy: string | null;
                lastTrained: Date | null;
                datasetSize: number | null;
                framework: string;
                size: string;
                parameters: string | null;
                dockerImage: string | null;
                containerId: string | null;
                modelPath: string | null;
                config: unknown;
            }[];
            timestamp: string;
        };
    }>;
    getModel: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            modelId: string;
        };
        output: {
            success: boolean;
            model: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                description: string | null;
                type: "embedding" | "llm" | "classification" | "generation" | "other";
                version: string;
                status: "idle" | "loading" | "running" | "error";
                accuracy: string | null;
                lastTrained: Date | null;
                datasetSize: number | null;
                framework: string;
                size: string;
                parameters: string | null;
                dockerImage: string | null;
                containerId: string | null;
                modelPath: string | null;
                config: unknown;
            };
            timestamp: string;
        };
    }>;
    getModelMetrics: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            modelId: string;
        };
        output: {
            success: boolean;
            metrics: any;
            modelId: string;
            timestamp: string;
        };
    }>;
    deployModel: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            modelId: string;
        };
        output: {
            success: boolean;
            containerId: string | undefined;
            message: string;
            timestamp: string;
        };
    }>;
    stopModel: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            modelId: string;
        };
        output: {
            success: boolean;
            message: string;
            timestamp: string;
        };
    }>;
    getTrainingJobs: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            jobs: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                status: "running" | "pending" | "completed" | "failed";
                accuracy: string | null;
                config: unknown;
                modelId: string;
                progress: number;
                startTime: Date;
                endTime: Date | null;
                epochs: number;
                currentEpoch: number;
                loss: string | null;
                batchSize: number | null;
                learningRate: string | null;
                datasetPath: string | null;
                logs: string | null;
            }[];
            timestamp: string;
        };
    }>;
    startTraining: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            modelId: string;
            epochs: number;
            batchSize: number;
            learningRate?: number | undefined;
            datasetPath?: string | undefined;
        };
        output: {
            success: boolean;
            jobId: string;
            message: string;
            timestamp: string;
        };
    }>;
    getTrainingJob: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            jobId: string;
        };
        output: {
            success: boolean;
            job: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                status: "running" | "pending" | "completed" | "failed";
                accuracy: string | null;
                config: unknown;
                modelId: string;
                progress: number;
                startTime: Date;
                endTime: Date | null;
                epochs: number;
                currentEpoch: number;
                loss: string | null;
                batchSize: number | null;
                learningRate: string | null;
                datasetPath: string | null;
                logs: string | null;
            };
            timestamp: string;
        };
    }>;
    deleteModel: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            modelId: string;
        };
        output: {
            success: boolean;
            message: string;
            timestamp: string;
        };
    }>;
}>;
//# sourceMappingURL=models.d.ts.map