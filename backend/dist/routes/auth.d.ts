export declare const authRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth").User | null;
        session: import("../auth").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    register: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            email: string;
            password: string;
            firstName?: string | undefined;
            lastName?: string | undefined;
            username?: string | undefined;
        };
        output: {
            user: {
                id: string;
                email: string;
                firstName: string | null;
                lastName: string | null;
                username: string | null;
            };
            sessionId: string;
        };
    }>;
    login: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            email: string;
            password: string;
        };
        output: {
            user: {
                id: string;
                email: string;
                firstName: string | null;
                lastName: string | null;
                username: string | null;
                role: string;
            };
            sessionId: string;
        };
    }>;
    logout: import("@trpc/server").TRPCMutationProcedure<{
        input: void;
        output: {
            success: boolean;
        };
    }>;
    session: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            user: {
                id: string;
                email: string;
                firstName: string | null;
                lastName: string | null;
                username: string | null;
                role: string;
                avatar: string | null;
            };
            session: {
                id: string;
                expiresAt: Date;
            };
        } | null;
    }>;
    changePassword: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            currentPassword: string;
            newPassword: string;
        };
        output: {
            success: boolean;
        };
    }>;
}>;
//# sourceMappingURL=auth.d.ts.map