import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { redisService } from '../services/cache/redis.js';
export const systemRouter = router({
    health: publicProcedure.query(async () => {
        try {
            // Check Redis connection
            await redisService.ping();
            return {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                services: {
                    redis: 'healthy',
                    postgres: 'healthy', // Add actual DB check
                    api: 'healthy'
                }
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'Unknown error',
                services: {
                    redis: 'unhealthy',
                    postgres: 'unknown',
                    api: 'healthy'
                }
            };
        }
    }),
    serviceStatus: publicProcedure.query(async () => {
        const services = [
            { id: 'postgres', name: 'PostgreSQL', status: 'healthy', port: 5435 },
            { id: 'redis', name: 'Red<PERSON>', status: 'healthy', port: 6379 },
            { id: 'milvus', name: 'Mil<PERSON><PERSON>', status: 'healthy', port: 19530 },
            { id: 'minio', name: '<PERSON><PERSON>', status: 'healthy', port: 9002 },
            { id: 'grafana', name: 'Grafana', status: 'healthy', port: 3000 },
            { id: 'prometheus', name: 'Prometheus', status: 'healthy', port: 9090 },
        ];
        return { services };
    }),
    stats: publicProcedure.query(async () => {
        return {
            totalUsers: 1248,
            activeUsers: 432,
            totalRequests: 15234,
            responseTime: '120ms',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            timestamp: new Date().toISOString()
        };
    }),
    restartService: publicProcedure
        .input(z.object({ serviceId: z.string() }))
        .mutation(async ({ input }) => {
        // In a real implementation, this would actually restart the service
        // For now, we'll just simulate it
        await new Promise(resolve => setTimeout(resolve, 2000));
        return {
            success: true,
            message: `Service ${input.serviceId} restarted successfully`,
            timestamp: new Date().toISOString()
        };
    }),
});
//# sourceMappingURL=system.js.map