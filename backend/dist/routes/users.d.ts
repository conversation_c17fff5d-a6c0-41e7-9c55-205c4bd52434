export declare const userRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth").User | null;
        session: import("../auth").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    me: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            id: string;
            email: string;
            username: string | null;
            passwordHash: string | null;
            firstName: string | null;
            lastName: string | null;
            avatar: string | null;
            bio: string | null;
            role: string;
            isActive: boolean;
            isVerified: boolean;
            lastLogin: Date | null;
            preferences: unknown;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    getById: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
        };
        output: {
            id: string;
            username: string | null;
            firstName: string | null;
            lastName: string | null;
            avatar: string | null;
            bio: string | null;
            createdAt: Date;
        };
    }>;
    updateProfile: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            firstName?: string | undefined;
            lastName?: string | undefined;
            bio?: string | undefined;
            avatar?: string | undefined;
        };
        output: {
            id: string;
            email: string;
            username: string | null;
            passwordHash: string | null;
            firstName: string | null;
            lastName: string | null;
            avatar: string | null;
            bio: string | null;
            role: string;
            isActive: boolean;
            isVerified: boolean;
            lastLogin: Date | null;
            preferences: unknown;
            createdAt: Date;
            updatedAt: Date;
        };
    }>;
    list: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            limit?: number | undefined;
            offset?: number | undefined;
            search?: string | undefined;
        };
        output: {
            id: string;
            email: string;
            username: string | null;
            firstName: string | null;
            lastName: string | null;
            role: string;
            isActive: boolean;
            isVerified: boolean;
            createdAt: Date;
            lastLogin: Date | null;
        }[];
    }>;
}>;
//# sourceMappingURL=users.d.ts.map