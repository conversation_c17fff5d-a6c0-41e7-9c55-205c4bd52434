import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { logger } from '../utils/logger.js';
import { etcdService } from '../services/etcd.js';
// Zod schemas for validation
const KeyValueSchema = z.object({
    key: z.string().min(1),
    value: z.string(),
});
const ServiceDiscoverySchema = z.object({
    serviceName: z.string().min(1),
    serviceUrl: z.string().url(),
    metadata: z.record(z.any()).optional(),
    ttl: z.number().min(1).default(60), // TTL in seconds
});
export const etcdRouter = router({
    // Health and connection
    health: publicProcedure.query(async () => {
        try {
            const healthData = await etcdService.isHealthy();
            return healthData;
        }
        catch (error) {
            logger.error('Etcd health check failed:', error);
            throw new Error('Etcd health check failed');
        }
    }),
    // Configuration management
    getAllKeys: publicProcedure.query(async () => {
        try {
            const keys = await etcdService.getAllKeys();
            return {
                success: true,
                keys,
                count: keys.length,
            };
        }
        catch (error) {
            logger.error('Failed to get all keys:', error);
            throw new Error('Failed to retrieve keys from etcd');
        }
    }),
    getKey: publicProcedure
        .input(z.object({ key: z.string() }))
        .query(async ({ input }) => {
        try {
            const keyValue = await etcdService.getKey(input.key);
            if (!keyValue) {
                return {
                    success: false,
                    message: 'Key not found',
                };
            }
            return {
                success: true,
                ...keyValue,
            };
        }
        catch (error) {
            logger.error('Failed to get key:', error);
            throw new Error(`Failed to get key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    putKey: publicProcedure
        .input(KeyValueSchema)
        .mutation(async ({ input }) => {
        try {
            const success = await etcdService.putKey(input.key, input.value);
            if (!success) {
                throw new Error('Failed to set key');
            }
            return {
                success: true,
                message: `Key ${input.key} set successfully`,
                key: input.key,
                value: input.value,
            };
        }
        catch (error) {
            logger.error('Failed to put key:', error);
            throw new Error(`Failed to set key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    deleteKey: publicProcedure
        .input(z.object({ key: z.string() }))
        .mutation(async ({ input }) => {
        try {
            const success = await etcdService.deleteKey(input.key);
            if (!success) {
                return {
                    success: false,
                    message: 'Key not found or could not be deleted',
                };
            }
            return {
                success: true,
                message: `Key ${input.key} deleted successfully`,
                key: input.key,
            };
        }
        catch (error) {
            logger.error('Failed to delete key:', error);
            throw new Error(`Failed to delete key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    getKeysByPrefix: publicProcedure
        .input(z.object({ prefix: z.string() }))
        .query(async ({ input }) => {
        try {
            const keys = await etcdService.getKeysByPrefix(input.prefix);
            return {
                success: true,
                keys,
                count: keys.length,
                prefix: input.prefix,
            };
        }
        catch (error) {
            logger.error('Failed to get keys by prefix:', error);
            throw new Error(`Failed to get keys by prefix: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Service Discovery
    discoverServices: publicProcedure.query(async () => {
        try {
            const services = await etcdService.discoverServices();
            const formattedServices = services.map(service => ({
                serviceName: service.serviceName,
                instances: service.instances.map(instance => ({
                    ...instance,
                    lastSeenAgo: Math.floor((Date.now() - new Date(instance.lastSeen).getTime()) / 1000),
                })),
                totalInstances: service.instances.length,
                healthyInstances: service.instances.filter(i => i.healthy).length,
            }));
            return {
                success: true,
                services: formattedServices,
                totalServices: formattedServices.length,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to discover services:', error);
            throw new Error('Failed to discover services');
        }
    }),
    getServiceInstances: publicProcedure
        .input(z.object({ serviceName: z.string() }))
        .query(async ({ input }) => {
        try {
            const services = await etcdService.discoverServices();
            const service = services.find(s => s.serviceName === input.serviceName);
            const instances = service?.instances || [];
            return {
                success: true,
                serviceName: input.serviceName,
                instances: instances.map(instance => ({
                    ...instance,
                    lastSeenAgo: Math.floor((Date.now() - new Date(instance.lastSeen).getTime()) / 1000),
                })),
                count: instances.length,
                healthyCount: instances.filter(i => i.healthy).length,
            };
        }
        catch (error) {
            logger.error('Failed to get service instances:', error);
            throw new Error(`Failed to get service instances: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    registerService: publicProcedure
        .input(ServiceDiscoverySchema)
        .mutation(async ({ input }) => {
        try {
            const serviceId = await etcdService.registerService(input.serviceName, input.serviceUrl, input.metadata, input.ttl);
            return {
                success: true,
                message: `Service ${input.serviceName} registered successfully`,
                serviceId,
                serviceName: input.serviceName,
            };
        }
        catch (error) {
            logger.error('Failed to register service:', error);
            throw new Error(`Failed to register service: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    deregisterService: publicProcedure
        .input(z.object({
        serviceName: z.string(),
        serviceId: z.string(),
    }))
        .mutation(async ({ input }) => {
        try {
            const success = await etcdService.deregisterService(input.serviceName, input.serviceId);
            if (!success) {
                return {
                    success: false,
                    message: 'Service instance not found',
                };
            }
            return {
                success: true,
                message: `Service instance ${input.serviceId} deregistered successfully`,
                serviceName: input.serviceName,
                serviceId: input.serviceId,
            };
        }
        catch (error) {
            logger.error('Failed to deregister service:', error);
            throw new Error(`Failed to deregister service: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Configuration templates
    getConfigurationTemplates: publicProcedure.query(async () => {
        try {
            const templates = [
                {
                    name: 'Database Configuration',
                    description: 'Standard database connection settings',
                    keys: [
                        { key: '/config/database/host', value: 'localhost', description: 'Database host' },
                        { key: '/config/database/port', value: '5432', description: 'Database port' },
                        { key: '/config/database/name', value: 'app_db', description: 'Database name' },
                        { key: '/config/database/username', value: 'app_user', description: 'Database username' },
                    ],
                },
                {
                    name: 'Redis Configuration',
                    description: 'Redis cache server settings',
                    keys: [
                        { key: '/config/redis/host', value: 'localhost', description: 'Redis host' },
                        { key: '/config/redis/port', value: '6379', description: 'Redis port' },
                        { key: '/config/redis/password', value: '', description: 'Redis password (optional)' },
                    ],
                },
                {
                    name: 'Vector Database Configuration',
                    description: 'Milvus vector database settings',
                    keys: [
                        { key: '/config/milvus/host', value: 'localhost', description: 'Milvus host' },
                        { key: '/config/milvus/port', value: '19530', description: 'Milvus port' },
                        { key: '/config/milvus/collection', value: 'default', description: 'Default collection' },
                    ],
                },
            ];
            return {
                success: true,
                templates,
                count: templates.length,
            };
        }
        catch (error) {
            logger.error('Failed to get configuration templates:', error);
            throw new Error('Failed to get configuration templates');
        }
    }),
    applyConfigurationTemplate: publicProcedure
        .input(z.object({
        templateName: z.string(),
        values: z.record(z.string()),
    }))
        .mutation(async ({ input }) => {
        try {
            const appliedKeys = [];
            // Apply the template values to etcd
            for (const [key, value] of Object.entries(input.values)) {
                const success = await etcdService.putKey(key, value);
                if (success) {
                    appliedKeys.push(key);
                }
            }
            return {
                success: true,
                message: `Template ${input.templateName} applied successfully`,
                appliedKeys,
                count: appliedKeys.length,
            };
        }
        catch (error) {
            logger.error('Failed to apply configuration template:', error);
            throw new Error(`Failed to apply template: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
    // Cluster information
    getClusterInfo: publicProcedure.query(async () => {
        try {
            const clusterInfo = await etcdService.getClusterInfo();
            return {
                success: true,
                cluster: clusterInfo,
                memberCount: clusterInfo.members.length,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to get cluster info:', error);
            throw new Error('Failed to get cluster information');
        }
    }),
    // Watch functionality (simplified)
    watchKey: publicProcedure
        .input(z.object({ key: z.string() }))
        .query(async ({ input }) => {
        try {
            const watchData = await etcdService.watchKey(input.key);
            return {
                success: true,
                ...watchData,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            logger.error('Failed to watch key:', error);
            throw new Error(`Failed to watch key: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }),
});
//# sourceMappingURL=etcd.js.map