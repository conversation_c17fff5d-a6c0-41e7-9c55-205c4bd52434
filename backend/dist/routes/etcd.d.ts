export declare const etcdRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    health: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            status: string;
            connected: boolean;
            timestamp: string;
            version?: string;
        };
    }>;
    getAllKeys: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            keys: import("../services/etcd.js").KeyValuePair[];
            count: number;
        };
    }>;
    getKey: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            key: string;
        };
        output: {
            success: boolean;
            message: string;
        } | {
            key: string;
            value: string;
            createRevision: number;
            modRevision: number;
            version: number;
            success: boolean;
            message?: undefined;
        };
    }>;
    putKey: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            value: string;
            key: string;
        };
        output: {
            success: boolean;
            message: string;
            key: string;
            value: string;
        };
    }>;
    deleteKey: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            key: string;
        };
        output: {
            success: boolean;
            message: string;
            key?: undefined;
        } | {
            success: boolean;
            message: string;
            key: string;
        };
    }>;
    getKeysByPrefix: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            prefix: string;
        };
        output: {
            success: boolean;
            keys: import("../services/etcd.js").KeyValuePair[];
            count: number;
            prefix: string;
        };
    }>;
    discoverServices: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            services: {
                serviceName: string;
                instances: {
                    lastSeenAgo: number;
                    id: string;
                    host: string;
                    port: number;
                    healthy: boolean;
                    lastSeen: string;
                    metadata?: Record<string, any>;
                    ttl?: number;
                }[];
                totalInstances: number;
                healthyInstances: number;
            }[];
            totalServices: number;
            timestamp: string;
        };
    }>;
    getServiceInstances: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            serviceName: string;
        };
        output: {
            success: boolean;
            serviceName: string;
            instances: {
                lastSeenAgo: number;
                id: string;
                host: string;
                port: number;
                healthy: boolean;
                lastSeen: string;
                metadata?: Record<string, any>;
                ttl?: number;
            }[];
            count: number;
            healthyCount: number;
        };
    }>;
    registerService: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            serviceName: string;
            serviceUrl: string;
            metadata?: Record<string, any> | undefined;
            ttl?: number | undefined;
        };
        output: {
            success: boolean;
            message: string;
            serviceId: string;
            serviceName: string;
        };
    }>;
    deregisterService: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            serviceId: string;
            serviceName: string;
        };
        output: {
            success: boolean;
            message: string;
            serviceName?: undefined;
            serviceId?: undefined;
        } | {
            success: boolean;
            message: string;
            serviceName: string;
            serviceId: string;
        };
    }>;
    getConfigurationTemplates: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            templates: {
                name: string;
                description: string;
                keys: {
                    key: string;
                    value: string;
                    description: string;
                }[];
            }[];
            count: number;
        };
    }>;
    applyConfigurationTemplate: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            values: Record<string, string>;
            templateName: string;
        };
        output: {
            success: boolean;
            message: string;
            appliedKeys: string[];
            count: number;
        };
    }>;
    getClusterInfo: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            cluster: import("../services/etcd.js").ClusterInfo;
            memberCount: number;
            timestamp: string;
        };
    }>;
    watchKey: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            key: string;
        };
        output: {
            timestamp: string;
            key: string;
            value: string | null;
            watching: boolean;
            watchId: string;
            success: boolean;
        };
    }>;
}>;
//# sourceMappingURL=etcd.d.ts.map