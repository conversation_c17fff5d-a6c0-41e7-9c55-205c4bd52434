import { z } from 'zod';
import { router, publicProcedure } from '../lib/trpc.js';
import { redisService } from '../services/cache/redis.js';
// Settings schemas
const GeneralSettingsSchema = z.object({
    appName: z.string().min(1),
    appDescription: z.string(),
    timezone: z.string(),
    theme: z.enum(['light', 'dark', 'auto']),
    language: z.string(),
});
const DatabaseSettingsSchema = z.object({
    maxConnections: z.number().min(1).max(1000),
    connectionTimeout: z.number().min(1000).max(60000),
    queryTimeout: z.number().min(1000).max(300000),
    enableLogging: z.boolean(),
    logLevel: z.enum(['error', 'warn', 'info', 'debug']),
});
const SecuritySettingsSchema = z.object({
    sessionTimeout: z.number().min(300).max(86400), // 5 minutes to 24 hours
    maxLoginAttempts: z.number().min(3).max(10),
    enableTwoFactor: z.boolean(),
    passwordMinLength: z.number().min(6).max(128),
    requireSpecialChars: z.boolean(),
    allowSignups: z.boolean(),
});
const NotificationSettingsSchema = z.object({
    emailEnabled: z.boolean(),
    slackEnabled: z.boolean(),
    webhookEnabled: z.boolean(),
    emailRecipients: z.array(z.string().email()),
    slackWebhook: z.string().url().optional(),
    webhookUrl: z.string().url().optional(),
    notifyOnErrors: z.boolean(),
    notifyOnBackups: z.boolean(),
    notifyOnDeployments: z.boolean(),
});
const BackupSettingsSchema = z.object({
    enabled: z.boolean(),
    schedule: z.string(), // cron expression
    retentionDays: z.number().min(1).max(365),
    storageLocation: z.enum(['local', 's3', 'gcp', 'azure']),
    encryptBackups: z.boolean(),
    compressionLevel: z.number().min(0).max(9),
});
const MonitoringSettingsSchema = z.object({
    metricsEnabled: z.boolean(),
    logRetentionDays: z.number().min(1).max(365),
    alertingEnabled: z.boolean(),
    performanceTracking: z.boolean(),
    errorTracking: z.boolean(),
    uptimeMonitoring: z.boolean(),
});
// Default settings
const DEFAULT_SETTINGS = {
    general: {
        appName: 'Backend GUI',
        appDescription: 'A comprehensive backend management interface',
        timezone: 'UTC',
        theme: 'auto',
        language: 'en',
    },
    database: {
        maxConnections: 100,
        connectionTimeout: 5000,
        queryTimeout: 30000,
        enableLogging: true,
        logLevel: 'info',
    },
    security: {
        sessionTimeout: 3600, // 1 hour
        maxLoginAttempts: 5,
        enableTwoFactor: false,
        passwordMinLength: 8,
        requireSpecialChars: true,
        allowSignups: true,
    },
    notifications: {
        emailEnabled: false,
        slackEnabled: false,
        webhookEnabled: false,
        emailRecipients: [],
        notifyOnErrors: true,
        notifyOnBackups: false,
        notifyOnDeployments: false,
    },
    backup: {
        enabled: false,
        schedule: '0 2 * * *', // Daily at 2 AM
        retentionDays: 30,
        storageLocation: 'local',
        encryptBackups: true,
        compressionLevel: 6,
    },
    monitoring: {
        metricsEnabled: true,
        logRetentionDays: 30,
        alertingEnabled: true,
        performanceTracking: true,
        errorTracking: true,
        uptimeMonitoring: true,
    },
};
const SETTINGS_KEY_PREFIX = 'settings:';
export const settingsRouter = router({
    // Get all settings
    getAll: publicProcedure.query(async () => {
        try {
            const settingsKeys = await redisService.keys(`${SETTINGS_KEY_PREFIX}*`);
            const settings = {};
            if (settingsKeys.length === 0) {
                // Return default settings if none exist
                return DEFAULT_SETTINGS;
            }
            for (const key of settingsKeys) {
                const category = key.replace(SETTINGS_KEY_PREFIX, '');
                const value = await redisService.get(key);
                if (value) {
                    settings[category] = JSON.parse(value);
                }
            }
            // Merge with defaults to ensure all fields exist
            return {
                general: { ...DEFAULT_SETTINGS.general, ...settings.general },
                database: { ...DEFAULT_SETTINGS.database, ...settings.database },
                security: { ...DEFAULT_SETTINGS.security, ...settings.security },
                notifications: { ...DEFAULT_SETTINGS.notifications, ...settings.notifications },
                backup: { ...DEFAULT_SETTINGS.backup, ...settings.backup },
                monitoring: { ...DEFAULT_SETTINGS.monitoring, ...settings.monitoring },
            };
        }
        catch (error) {
            console.error('Error fetching settings:', error);
            return DEFAULT_SETTINGS;
        }
    }),
    // Get specific category settings
    getCategory: publicProcedure
        .input(z.object({ category: z.enum(['general', 'database', 'security', 'notifications', 'backup', 'monitoring']) }))
        .query(async ({ input }) => {
        try {
            const value = await redisService.get(`${SETTINGS_KEY_PREFIX}${input.category}`);
            if (value) {
                return JSON.parse(value);
            }
            return DEFAULT_SETTINGS[input.category];
        }
        catch (error) {
            console.error(`Error fetching ${input.category} settings:`, error);
            return DEFAULT_SETTINGS[input.category];
        }
    }),
    // Update general settings
    updateGeneral: publicProcedure
        .input(GeneralSettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}general`, JSON.stringify(input));
            return {
                success: true,
                message: 'General settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating general settings:', error);
            throw new Error('Failed to update general settings');
        }
    }),
    // Update database settings
    updateDatabase: publicProcedure
        .input(DatabaseSettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}database`, JSON.stringify(input));
            return {
                success: true,
                message: 'Database settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating database settings:', error);
            throw new Error('Failed to update database settings');
        }
    }),
    // Update security settings
    updateSecurity: publicProcedure
        .input(SecuritySettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}security`, JSON.stringify(input));
            return {
                success: true,
                message: 'Security settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating security settings:', error);
            throw new Error('Failed to update security settings');
        }
    }),
    // Update notification settings
    updateNotifications: publicProcedure
        .input(NotificationSettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}notifications`, JSON.stringify(input));
            return {
                success: true,
                message: 'Notification settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating notification settings:', error);
            throw new Error('Failed to update notification settings');
        }
    }),
    // Update backup settings
    updateBackup: publicProcedure
        .input(BackupSettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}backup`, JSON.stringify(input));
            return {
                success: true,
                message: 'Backup settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating backup settings:', error);
            throw new Error('Failed to update backup settings');
        }
    }),
    // Update monitoring settings
    updateMonitoring: publicProcedure
        .input(MonitoringSettingsSchema)
        .mutation(async ({ input }) => {
        try {
            await redisService.set(`${SETTINGS_KEY_PREFIX}monitoring`, JSON.stringify(input));
            return {
                success: true,
                message: 'Monitoring settings updated successfully',
                data: input,
            };
        }
        catch (error) {
            console.error('Error updating monitoring settings:', error);
            throw new Error('Failed to update monitoring settings');
        }
    }),
    // Reset settings to defaults
    resetToDefaults: publicProcedure
        .input(z.object({ category: z.enum(['general', 'database', 'security', 'notifications', 'backup', 'monitoring']).optional() }))
        .mutation(async ({ input }) => {
        try {
            if (input.category) {
                // Reset specific category
                await redisService.set(`${SETTINGS_KEY_PREFIX}${input.category}`, JSON.stringify(DEFAULT_SETTINGS[input.category]));
                return {
                    success: true,
                    message: `${input.category} settings reset to defaults`,
                    data: DEFAULT_SETTINGS[input.category],
                };
            }
            else {
                // Reset all settings
                for (const [category, defaults] of Object.entries(DEFAULT_SETTINGS)) {
                    await redisService.set(`${SETTINGS_KEY_PREFIX}${category}`, JSON.stringify(defaults));
                }
                return {
                    success: true,
                    message: 'All settings reset to defaults',
                    data: DEFAULT_SETTINGS,
                };
            }
        }
        catch (error) {
            console.error('Error resetting settings:', error);
            throw new Error('Failed to reset settings');
        }
    }),
    // Export settings
    export: publicProcedure.query(async () => {
        try {
            const settingsKeys = await redisService.keys(`${SETTINGS_KEY_PREFIX}*`);
            const settings = {};
            for (const key of settingsKeys) {
                const category = key.replace(SETTINGS_KEY_PREFIX, '');
                const value = await redisService.get(key);
                if (value) {
                    settings[category] = JSON.parse(value);
                }
            }
            return {
                settings,
                exportedAt: new Date().toISOString(),
                version: '1.0.0',
            };
        }
        catch (error) {
            console.error('Error exporting settings:', error);
            throw new Error('Failed to export settings');
        }
    }),
    // Import settings
    importSettings: publicProcedure
        .input(z.object({
        settings: z.record(z.any()),
        overwrite: z.boolean().default(true),
    }))
        .mutation(async ({ input }) => {
        try {
            const { settings, overwrite } = input;
            const imported = [];
            for (const [category, data] of Object.entries(settings)) {
                if (DEFAULT_SETTINGS.hasOwnProperty(category)) {
                    const key = `${SETTINGS_KEY_PREFIX}${category}`;
                    if (overwrite || !(await redisService.exists(key))) {
                        await redisService.set(key, JSON.stringify(data));
                        imported.push(category);
                    }
                }
            }
            return {
                success: true,
                message: `Imported settings for: ${imported.join(', ')}`,
                imported,
            };
        }
        catch (error) {
            console.error('Error importing settings:', error);
            throw new Error('Failed to import settings');
        }
    }),
    // Get system info for settings validation
    getSystemInfo: publicProcedure.query(async () => {
        return {
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            env: process.env.NODE_ENV || 'development',
            timezones: [
                'UTC', 'America/New_York', 'America/Los_Angeles', 'Europe/London',
                'Europe/Paris', 'Asia/Tokyo', 'Asia/Shanghai', 'Australia/Sydney'
            ],
            languages: [
                { code: 'en', name: 'English' },
                { code: 'es', name: 'Spanish' },
                { code: 'fr', name: 'French' },
                { code: 'de', name: 'German' },
                { code: 'ja', name: 'Japanese' },
                { code: 'zh', name: 'Chinese' },
            ],
        };
    }),
});
//# sourceMappingURL=settings.js.map