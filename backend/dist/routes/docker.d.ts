export declare const dockerRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    isRunning: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            running: boolean;
            timestamp: string;
            error?: undefined;
        } | {
            running: boolean;
            error: string;
            timestamp: string;
        };
    }>;
    listContainers: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            all?: boolean | undefined;
        } | undefined;
        output: {
            success: boolean;
            containers: import("../services/docker.js").ContainerInfo[];
            count: number;
        };
    }>;
    getContainer: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            container: import("../services/docker.js").ContainerInfo;
        };
    }>;
    startContainer: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    stopContainer: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    restartContainer: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    removeContainer: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    getContainerLogs: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
            tail?: number | undefined;
        };
        output: {
            success: boolean;
            logs: string;
            containerId: string;
            lines: number;
        };
    }>;
    startMultipleContainers: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            ids: string[];
        };
        output: {
            success: boolean;
            message: string;
            successful: number;
            failed: number;
            total: number;
        };
    }>;
    stopMultipleContainers: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            ids: string[];
        };
        output: {
            success: boolean;
            message: string;
            successful: number;
            failed: number;
            total: number;
        };
    }>;
    composeUp: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            services?: string[] | undefined;
            detached?: boolean | undefined;
            build?: boolean | undefined;
            recreate?: boolean | undefined;
        } | undefined;
        output: {
            success: boolean;
            message: string;
            output: string;
            options: {
                detached: boolean;
                build: boolean;
                recreate: boolean;
                services?: string[] | undefined;
            } | undefined;
        };
    }>;
    composeDown: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            removeVolumes?: boolean | undefined;
            removeImages?: boolean | undefined;
            removeOrphans?: boolean | undefined;
        } | undefined;
        output: {
            success: boolean;
            message: string;
            output: string;
            options: {
                removeVolumes: boolean;
                removeImages: boolean;
                removeOrphans: boolean;
            } | undefined;
        };
    }>;
    composeBuild: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            service?: string | undefined;
            noCache?: boolean | undefined;
        } | undefined;
        output: {
            success: boolean;
            message: string;
            output: string;
        };
    }>;
    composePull: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            service?: string | undefined;
        } | undefined;
        output: {
            success: boolean;
            message: string;
            output: string;
        };
    }>;
    getComposeStatus: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            status: {
                isRunning: boolean;
                services: number;
                runningServices: number;
                containers: Array<{
                    name: string;
                    service: string;
                    status: string;
                    health?: string;
                }>;
            };
        };
    }>;
    getComposeServices: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            services: {
                name: string;
                image: string;
                status: string;
                ports: string[];
            }[];
        };
    }>;
    getImages: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            images: {
                id: string;
                repository: string;
                tag: string;
                size: number;
                created: string;
            }[];
            count: number;
        };
    }>;
    removeImage: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
            force?: boolean | undefined;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    getNetworks: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            networks: {
                id: string;
                name: string;
                driver: string;
                scope: string;
                created: string;
                containers: number;
            }[];
            count: number;
        };
    }>;
    getVolumes: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            volumes: {
                name: string;
                driver: string;
                mountpoint: string;
                created: string;
                size?: number;
            }[];
            count: number;
        };
    }>;
    removeVolume: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            name: string;
            force?: boolean | undefined;
        };
        output: {
            success: boolean;
            message: string;
        };
    }>;
    getContainerStats: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            stats: {
                cpuUsage: number;
                memoryUsage: number;
                memoryLimit: number;
                networkRx: number;
                networkTx: number;
            } | undefined;
            containerId: string;
            timestamp: string;
        };
    }>;
    getSystemInfo: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            info: {
                dockerRunning: boolean;
                totalContainers: number;
                runningContainers: number;
                stoppedContainers: number;
                systemDf: {
                    containers: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                    images: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                    volumes: {
                        active: number;
                        total: number;
                        size: number;
                        reclaimable: number;
                    };
                };
                timestamp: string;
            };
        };
    }>;
    getSystemDf: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            df: {
                containers: {
                    active: number;
                    total: number;
                    size: number;
                    reclaimable: number;
                };
                images: {
                    active: number;
                    total: number;
                    size: number;
                    reclaimable: number;
                };
                volumes: {
                    active: number;
                    total: number;
                    size: number;
                    reclaimable: number;
                };
            };
            timestamp: string;
        };
    }>;
}>;
//# sourceMappingURL=docker.d.ts.map