export declare const systemRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    health: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            status: string;
            timestamp: string;
            services: {
                redis: string;
                postgres: string;
                api: string;
            };
            error?: undefined;
        } | {
            status: string;
            timestamp: string;
            error: string;
            services: {
                redis: string;
                postgres: string;
                api: string;
            };
        };
    }>;
    serviceStatus: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            services: {
                id: string;
                name: string;
                status: string;
                port: number;
            }[];
        };
    }>;
    stats: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            totalUsers: number;
            activeUsers: number;
            totalRequests: number;
            responseTime: string;
            uptime: number;
            memory: NodeJS.MemoryUsage;
            timestamp: string;
        };
    }>;
    restartService: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            serviceId: string;
        };
        output: {
            success: boolean;
            message: string;
            timestamp: string;
        };
    }>;
}>;
//# sourceMappingURL=system.d.ts.map