export declare const dockerRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    status: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            data: {
                running: boolean;
                message: string;
            };
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    containers: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            all?: boolean | undefined;
        };
        output: {
            success: boolean;
            data: import("../services/docker.js").ContainerInfo[];
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    container: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
        };
        output: {
            success: boolean;
            data: import("../services/docker.js").ContainerInfo | null;
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    containerAction: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            id: string;
            action: "start" | "stop" | "restart";
        };
        output: {
            success: boolean;
            data: {
                action: "start" | "stop" | "restart";
                containerId: string;
                result: boolean;
            };
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    logs: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            id: string;
            tail?: number | undefined;
        };
        output: {
            success: boolean;
            data: {
                containerId: string;
                logs: string;
                tail: number;
            };
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
}>;
export declare const systemRouterReal: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    metrics: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            data: import("../services/system-monitor.js").SystemMetrics;
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    health: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            data: {
                status: string;
                services: (import("../services/system-monitor.js").ServiceHealth | {
                    name: string;
                    status: string;
                    lastCheck: string;
                    error: string | undefined;
                })[];
                docker: {
                    running: boolean;
                };
                timestamp: string;
            };
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    processes: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            data: {
                pid: number;
                name: string;
                cpu: number;
                memory: number;
                command: string;
            }[];
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
    stats: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            data: {
                system: {
                    uptime: string;
                    hostname: string;
                    platform: string;
                    arch: string;
                };
                resources: {
                    cpu: {
                        usage: number;
                        cores: number;
                        temperature: number | undefined;
                    };
                    memory: {
                        used: string;
                        total: string;
                        percentage: number;
                    };
                    disk: {
                        used: string;
                        total: string;
                        percentage: number;
                    };
                    network: {
                        in: string;
                        out: string;
                    };
                };
                containers: {
                    total: number;
                    running: number;
                    stopped: number;
                };
                docker: {
                    running: boolean;
                };
            };
            error?: undefined;
        } | {
            success: boolean;
            error: string;
            data?: undefined;
        };
    }>;
}>;
export declare const realDataRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, import("@trpc/server/unstable-core-do-not-import").DecorateCreateRouterOptions<{
    docker: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        status: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                data: {
                    running: boolean;
                    message: string;
                };
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        containers: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                all?: boolean | undefined;
            };
            output: {
                success: boolean;
                data: import("../services/docker.js").ContainerInfo[];
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        container: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
            };
            output: {
                success: boolean;
                data: import("../services/docker.js").ContainerInfo | null;
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        containerAction: import("@trpc/server").TRPCMutationProcedure<{
            input: {
                id: string;
                action: "start" | "stop" | "restart";
            };
            output: {
                success: boolean;
                data: {
                    action: "start" | "stop" | "restart";
                    containerId: string;
                    result: boolean;
                };
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        logs: import("@trpc/server").TRPCQueryProcedure<{
            input: {
                id: string;
                tail?: number | undefined;
            };
            output: {
                success: boolean;
                data: {
                    containerId: string;
                    logs: string;
                    tail: number;
                };
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
    }>;
    system: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
        ctx: {
            db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
                $client: import("postgres").Sql<{}>;
            };
            user: import("../auth/index.js").User | null;
            session: import("../auth/index.js").Session | null;
            req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
            res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
        };
        meta: object;
        errorShape: {
            data: {
                zodError: string | null;
                code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
                httpStatus: number;
                path?: string;
                stack?: string;
            };
            message: string;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
        };
        transformer: true;
    }, {
        metrics: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                data: import("../services/system-monitor.js").SystemMetrics;
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        health: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                data: {
                    status: string;
                    services: (import("../services/system-monitor.js").ServiceHealth | {
                        name: string;
                        status: string;
                        lastCheck: string;
                        error: string | undefined;
                    })[];
                    docker: {
                        running: boolean;
                    };
                    timestamp: string;
                };
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        processes: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                data: {
                    pid: number;
                    name: string;
                    cpu: number;
                    memory: number;
                    command: string;
                }[];
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
        stats: import("@trpc/server").TRPCQueryProcedure<{
            input: void;
            output: {
                success: boolean;
                data: {
                    system: {
                        uptime: string;
                        hostname: string;
                        platform: string;
                        arch: string;
                    };
                    resources: {
                        cpu: {
                            usage: number;
                            cores: number;
                            temperature: number | undefined;
                        };
                        memory: {
                            used: string;
                            total: string;
                            percentage: number;
                        };
                        disk: {
                            used: string;
                            total: string;
                            percentage: number;
                        };
                        network: {
                            in: string;
                            out: string;
                        };
                    };
                    containers: {
                        total: number;
                        running: number;
                        stopped: number;
                    };
                    docker: {
                        running: boolean;
                    };
                };
                error?: undefined;
            } | {
                success: boolean;
                error: string;
                data?: undefined;
            };
        }>;
    }>;
}>>;
//# sourceMappingURL=real-data.d.ts.map