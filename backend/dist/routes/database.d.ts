export declare const databaseRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    health: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            status: string;
            connected: boolean;
            timestamp: string;
            testQuery: boolean;
            error?: undefined;
        } | {
            status: string;
            connected: boolean;
            timestamp: string;
            error: string;
            testQuery?: undefined;
        };
    }>;
    testConnection: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            username?: string | undefined;
            host?: string | undefined;
            port?: number | undefined;
            database?: string | undefined;
            password?: string | undefined;
        };
        output: {
            success: boolean;
            message: string;
            timestamp: string;
        };
    }>;
    getTables: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            tables: {
                name: string;
                type: string;
                schema: string;
            }[];
        };
    }>;
    getTableSchema: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            tableName: string;
        };
        output: {
            success: boolean;
            tableName: "users" | "sessions" | "ai_models" | "training_jobs" | "model_metrics";
            columns: {
                name: any;
                type: any;
                nullable: boolean;
                default: any;
                maxLength: any;
                precision: any;
                scale: any;
            }[];
        };
    }>;
    getTableIndexes: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            tableName: string;
        };
        output: {
            success: boolean;
            tableName: string;
            indexes: {
                name: any;
                column: any;
                unique: any;
                primary: any;
            }[];
        };
    }>;
    executeQuery: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            query: string;
            params?: any[] | undefined;
        };
        output: {
            success: boolean;
            query: string;
            rows: import("postgres").RowList<Record<string, unknown>[]>;
            rowCount: number;
            executionTime: number;
            timestamp: string;
        };
    }>;
    getTableData: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            tableName: string;
            orderBy?: string | undefined;
            limit?: number | undefined;
            offset?: number | undefined;
            orderDirection?: "desc" | "asc" | undefined;
            filters?: Record<string, any> | undefined;
        };
        output: {
            success: boolean;
            tableName: "users" | "sessions" | "ai_models" | "training_jobs" | "model_metrics";
            data: import("postgres").RowList<Record<string, unknown>[]>;
            pagination: {
                limit: number;
                offset: number;
                total: number;
                pages: number;
                currentPage: number;
            };
            executionTime: number;
            timestamp: string;
        };
    }>;
    getDatabaseStats: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            database: {
                name: unknown;
                size: unknown;
            };
            tables: {
                schema: any;
                name: any;
                stats: {
                    inserts: number;
                    updates: number;
                    deletes: number;
                    liveTuples: number;
                    deadTuples: number;
                    lastVacuum: any;
                    lastAutoVacuum: any;
                    lastAnalyze: any;
                    lastAutoAnalyze: any;
                };
            }[];
            timestamp: string;
        };
    }>;
    getActiveConnections: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            success: boolean;
            connections: {
                pid: any;
                username: any;
                applicationName: any;
                clientAddress: any;
                clientPort: any;
                backendStart: any;
                state: any;
                queryStart: any;
                query: any;
            }[];
            count: number;
            timestamp: string;
        };
    }>;
}>;
//# sourceMappingURL=database.d.ts.map