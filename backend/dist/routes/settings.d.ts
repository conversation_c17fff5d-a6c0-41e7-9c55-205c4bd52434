export declare const settingsRouter: import("@trpc/server/unstable-core-do-not-import").BuiltRouter<{
    ctx: {
        db: import("drizzle-orm/postgres-js").PostgresJsDatabase<typeof import("../db/schemas/index.js")> & {
            $client: import("postgres").Sql<{}>;
        };
        user: import("../auth/index.js").User | null;
        session: import("../auth/index.js").Session | null;
        req: import("fastify").FastifyRequest<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown, import("fastify").FastifyBaseLogger, import("fastify/types/type-provider.js").ResolveFastifyRequestType<import("fastify").FastifyTypeProviderDefault, import("fastify").FastifySchema, import("fastify").RouteGenericInterface>>;
        res: import("fastify").FastifyReply<import("fastify").RouteGenericInterface, import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    };
    meta: object;
    errorShape: {
        data: {
            zodError: string | null;
            code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_KEY;
            httpStatus: number;
            path?: string;
            stack?: string;
        };
        message: string;
        code: import("@trpc/server/unstable-core-do-not-import").TRPC_ERROR_CODE_NUMBER;
    };
    transformer: true;
}, {
    getAll: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            general: any;
            database: any;
            security: any;
            notifications: any;
            backup: any;
            monitoring: any;
        };
    }>;
    getCategory: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            category: "database" | "general" | "security" | "notifications" | "backup" | "monitoring";
        };
        output: any;
    }>;
    updateGeneral: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            appName: string;
            appDescription: string;
            timezone: string;
            theme: "light" | "dark" | "auto";
            language: string;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                appName: string;
                appDescription: string;
                timezone: string;
                theme: "light" | "dark" | "auto";
                language: string;
            };
        };
    }>;
    updateDatabase: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            logLevel: "error" | "debug" | "info" | "warn";
            maxConnections: number;
            connectionTimeout: number;
            queryTimeout: number;
            enableLogging: boolean;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                logLevel: "error" | "debug" | "info" | "warn";
                maxConnections: number;
                connectionTimeout: number;
                queryTimeout: number;
                enableLogging: boolean;
            };
        };
    }>;
    updateSecurity: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            sessionTimeout: number;
            maxLoginAttempts: number;
            enableTwoFactor: boolean;
            passwordMinLength: number;
            requireSpecialChars: boolean;
            allowSignups: boolean;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                sessionTimeout: number;
                maxLoginAttempts: number;
                enableTwoFactor: boolean;
                passwordMinLength: number;
                requireSpecialChars: boolean;
                allowSignups: boolean;
            };
        };
    }>;
    updateNotifications: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            emailEnabled: boolean;
            slackEnabled: boolean;
            webhookEnabled: boolean;
            emailRecipients: string[];
            notifyOnErrors: boolean;
            notifyOnBackups: boolean;
            notifyOnDeployments: boolean;
            slackWebhook?: string | undefined;
            webhookUrl?: string | undefined;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                emailEnabled: boolean;
                slackEnabled: boolean;
                webhookEnabled: boolean;
                emailRecipients: string[];
                notifyOnErrors: boolean;
                notifyOnBackups: boolean;
                notifyOnDeployments: boolean;
                slackWebhook?: string | undefined;
                webhookUrl?: string | undefined;
            };
        };
    }>;
    updateBackup: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            enabled: boolean;
            schedule: string;
            retentionDays: number;
            storageLocation: "local" | "s3" | "gcp" | "azure";
            encryptBackups: boolean;
            compressionLevel: number;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                enabled: boolean;
                schedule: string;
                retentionDays: number;
                storageLocation: "local" | "s3" | "gcp" | "azure";
                encryptBackups: boolean;
                compressionLevel: number;
            };
        };
    }>;
    updateMonitoring: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            metricsEnabled: boolean;
            logRetentionDays: number;
            alertingEnabled: boolean;
            performanceTracking: boolean;
            errorTracking: boolean;
            uptimeMonitoring: boolean;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                metricsEnabled: boolean;
                logRetentionDays: number;
                alertingEnabled: boolean;
                performanceTracking: boolean;
                errorTracking: boolean;
                uptimeMonitoring: boolean;
            };
        };
    }>;
    resetToDefaults: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            category?: "database" | "general" | "security" | "notifications" | "backup" | "monitoring" | undefined;
        };
        output: {
            success: boolean;
            message: string;
            data: {
                appName: string;
                appDescription: string;
                timezone: string;
                theme: "auto";
                language: string;
            } | {
                maxConnections: number;
                connectionTimeout: number;
                queryTimeout: number;
                enableLogging: boolean;
                logLevel: "info";
            } | {
                sessionTimeout: number;
                maxLoginAttempts: number;
                enableTwoFactor: boolean;
                passwordMinLength: number;
                requireSpecialChars: boolean;
                allowSignups: boolean;
            } | {
                emailEnabled: boolean;
                slackEnabled: boolean;
                webhookEnabled: boolean;
                emailRecipients: never[];
                notifyOnErrors: boolean;
                notifyOnBackups: boolean;
                notifyOnDeployments: boolean;
            } | {
                enabled: boolean;
                schedule: string;
                retentionDays: number;
                storageLocation: "local";
                encryptBackups: boolean;
                compressionLevel: number;
            } | {
                metricsEnabled: boolean;
                logRetentionDays: number;
                alertingEnabled: boolean;
                performanceTracking: boolean;
                errorTracking: boolean;
                uptimeMonitoring: boolean;
            };
        } | {
            success: boolean;
            message: string;
            data: {
                general: {
                    appName: string;
                    appDescription: string;
                    timezone: string;
                    theme: "auto";
                    language: string;
                };
                database: {
                    maxConnections: number;
                    connectionTimeout: number;
                    queryTimeout: number;
                    enableLogging: boolean;
                    logLevel: "info";
                };
                security: {
                    sessionTimeout: number;
                    maxLoginAttempts: number;
                    enableTwoFactor: boolean;
                    passwordMinLength: number;
                    requireSpecialChars: boolean;
                    allowSignups: boolean;
                };
                notifications: {
                    emailEnabled: boolean;
                    slackEnabled: boolean;
                    webhookEnabled: boolean;
                    emailRecipients: never[];
                    notifyOnErrors: boolean;
                    notifyOnBackups: boolean;
                    notifyOnDeployments: boolean;
                };
                backup: {
                    enabled: boolean;
                    schedule: string;
                    retentionDays: number;
                    storageLocation: "local";
                    encryptBackups: boolean;
                    compressionLevel: number;
                };
                monitoring: {
                    metricsEnabled: boolean;
                    logRetentionDays: number;
                    alertingEnabled: boolean;
                    performanceTracking: boolean;
                    errorTracking: boolean;
                    uptimeMonitoring: boolean;
                };
            };
        };
    }>;
    export: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            settings: any;
            exportedAt: string;
            version: string;
        };
    }>;
    importSettings: import("@trpc/server").TRPCMutationProcedure<{
        input: {
            settings: Record<string, any>;
            overwrite?: boolean | undefined;
        };
        output: {
            success: boolean;
            message: string;
            imported: string[];
        };
    }>;
    getSystemInfo: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            nodeVersion: string;
            platform: NodeJS.Platform;
            arch: NodeJS.Architecture;
            uptime: number;
            memory: NodeJS.MemoryUsage;
            env: string;
            timezones: string[];
            languages: {
                code: string;
                name: string;
            }[];
        };
    }>;
}>;
//# sourceMappingURL=settings.d.ts.map