import { TRPCError } from '@trpc/server';
import { eq } from 'drizzle-orm';
import * as v from 'valibot';
import { hash, verify } from '@node-rs/argon2';
import { router, publicProcedure, protectedProcedure } from '../lib/trpc';
import { users } from '../db/schemas/users';
import { createSession, invalidateSession } from '../auth';
// Input validation schemas
const registerSchema = v.object({
    email: v.pipe(v.string(), v.email(), v.maxLength(255)),
    password: v.pipe(v.string(), v.minLength(8), v.maxLength(128)),
    firstName: v.optional(v.pipe(v.string(), v.maxLength(100))),
    lastName: v.optional(v.pipe(v.string(), v.maxLength(100))),
    username: v.optional(v.pipe(v.string(), v.minLength(3), v.maxLength(50))),
});
const loginSchema = v.object({
    email: v.pipe(v.string(), v.email()),
    password: v.pipe(v.string(), v.minLength(1)),
});
const changePasswordSchema = v.object({
    currentPassword: v.pipe(v.string(), v.minLength(1)),
    newPassword: v.pipe(v.string(), v.minLength(8), v.maxLength(128)),
});
export const authRouter = router({
    // Register new user
    register: publicProcedure
        .input((val) => v.parse(registerSchema, val))
        .mutation(async ({ ctx, input }) => {
        const { email, password, firstName, lastName, username } = input;
        // Check if user already exists
        const existingUser = await ctx.db.query.users.findFirst({
            where: eq(users.email, email.toLowerCase()),
        });
        if (existingUser) {
            throw new TRPCError({
                code: 'CONFLICT',
                message: 'User with this email already exists',
            });
        }
        // Check username uniqueness if provided
        if (username) {
            const existingUsername = await ctx.db.query.users.findFirst({
                where: eq(users.username, username),
            });
            if (existingUsername) {
                throw new TRPCError({
                    code: 'CONFLICT',
                    message: 'Username already taken',
                });
            }
        }
        // Hash password
        const hashedPassword = await hash(password, {
            memoryCost: 19456,
            timeCost: 2,
            outputLen: 32,
            parallelism: 1,
        });
        // Create user
        const [newUser] = await ctx.db
            .insert(users)
            .values({
            email: email.toLowerCase(),
            firstName,
            lastName,
            username,
            passwordHash: hashedPassword,
        })
            .returning();
        // Create session
        const session = await createSession(newUser.id, {});
        return {
            user: {
                id: newUser.id,
                email: newUser.email,
                firstName: newUser.firstName,
                lastName: newUser.lastName,
                username: newUser.username,
            },
            sessionId: session.id,
        };
    }),
    // Login user
    login: publicProcedure
        .input((val) => v.parse(loginSchema, val))
        .mutation(async ({ ctx, input }) => {
        const { email, password } = input;
        // Find user by email
        const user = await ctx.db.query.users.findFirst({
            where: eq(users.email, email.toLowerCase()),
        });
        if (!user) {
            throw new TRPCError({
                code: 'UNAUTHORIZED',
                message: 'Invalid email or password',
            });
        }
        // Verify password
        if (!user.passwordHash) {
            throw new TRPCError({
                code: 'UNAUTHORIZED',
                message: 'Invalid email or password',
            });
        }
        const validPassword = await verify(user.passwordHash, password, {
            memoryCost: 19456,
            timeCost: 2,
            outputLen: 32,
            parallelism: 1,
        });
        if (!validPassword) {
            throw new TRPCError({
                code: 'UNAUTHORIZED',
                message: 'Invalid email or password',
            });
        }
        const session = await createSession(user.id, {});
        // Update lastLogin
        await ctx.db
            .update(users)
            .set({ lastLogin: new Date() })
            .where(eq(users.id, user.id));
        return {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                username: user.username,
                role: user.role,
            },
            sessionId: session.id,
        };
    }),
    // Logout user
    logout: protectedProcedure.mutation(async ({ ctx }) => {
        await invalidateSession(ctx.session.id);
        return { success: true };
    }),
    // Get current session info
    session: publicProcedure.query(async ({ ctx }) => {
        if (!ctx.user || !ctx.session) {
            return null;
        }
        return {
            user: {
                id: ctx.user.id,
                email: ctx.user.email,
                firstName: ctx.user.firstName,
                lastName: ctx.user.lastName,
                username: ctx.user.username,
                role: ctx.user.role,
                avatar: ctx.user.avatar,
            },
            session: {
                id: ctx.session.id,
                expiresAt: ctx.session.expiresAt,
            },
        };
    }),
    // Change password
    changePassword: protectedProcedure
        .input((val) => v.parse(changePasswordSchema, val))
        .mutation(async ({ ctx, input }) => {
        const { currentPassword, newPassword } = input;
        // Note: You'll need to implement password verification
        // and updating based on your schema
        // Hash new password
        const hashedPassword = await hash(newPassword, {
            memoryCost: 19456,
            timeCost: 2,
            outputLen: 32,
            parallelism: 1,
        });
        // Update password in database
        // You'll need to add this field to your schema
        return { success: true };
    }),
});
//# sourceMappingURL=auth.js.map