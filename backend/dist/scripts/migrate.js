import { config } from 'dotenv';
config();
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import { db, client } from '../db';
async function runMigrations() {
    console.log('🚀 Running migrations...');
    try {
        await migrate(db, { migrationsFolder: './src/db/migrations' });
        console.log('✅ Migrations completed successfully!');
    }
    catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
    finally {
        await client.end();
        process.exit(0);
    }
}
runMigrations();
//# sourceMappingURL=migrate.js.map