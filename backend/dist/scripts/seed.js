import { config } from 'dotenv';
config();
import { db } from '../db/index.js';
import { aiModels, trainingJobs, modelMetrics } from '../db/schemas/models.js';
async function seed() {
    console.log('🌱 Seeding database...');
    try {
        // Insert sample AI models
        const models = await db.insert(aiModels).values([
            {
                id: '550e8400-e29b-41d4-a716-446655440001',
                name: 'BERT Base Embeddings',
                type: 'embedding',
                version: '1.2.0',
                status: 'idle',
                accuracy: '0.94',
                lastTrained: new Date('2024-01-15'),
                datasetSize: 100000,
                framework: 'PyTorch',
                size: '420MB',
                parameters: '110M',
                description: 'BERT base model for text embeddings',
                dockerImage: 'pytorch/pytorch:latest',
                modelPath: '/models/bert-base',
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440002',
                name: 'GPT-3.5 Turbo',
                type: 'llm',
                version: '2.1.0',
                status: 'idle',
                accuracy: '0.89',
                lastTrained: new Date('2024-01-10'),
                datasetSize: 500000,
                framework: 'TensorFlow',
                size: '2.1GB',
                parameters: '175B',
                description: 'Large language model for text generation',
                dockerImage: 'tensorflow/serving:latest',
                modelPath: '/models/gpt-3.5',
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440003',
                name: 'Sentiment Classifier',
                type: 'classification',
                version: '1.0.0',
                status: 'idle',
                accuracy: '0.87',
                lastTrained: new Date('2024-01-08'),
                datasetSize: 50000,
                framework: 'scikit-learn',
                size: '15MB',
                parameters: '1M',
                description: 'Binary sentiment classification model',
                dockerImage: 'python:3.9-slim',
                modelPath: '/models/sentiment',
            },
            {
                id: '550e8400-e29b-41d4-a716-446655440004',
                name: 'Stable Diffusion',
                type: 'generation',
                version: '1.5.0',
                status: 'idle',
                accuracy: '0.91',
                lastTrained: new Date('2024-01-12'),
                datasetSize: 1000000,
                framework: 'PyTorch',
                size: '4.2GB',
                parameters: '860M',
                description: 'Text-to-image generation model',
                dockerImage: 'pytorch/pytorch:latest',
                modelPath: '/models/stable-diffusion',
            },
        ]).onConflictDoNothing().returning();
        console.log(`✅ Inserted ${models.length} models`);
        // Insert sample training jobs
        const jobs = await db.insert(trainingJobs).values([
            {
                id: '650e8400-e29b-41d4-a716-446655440001',
                modelId: '550e8400-e29b-41d4-a716-446655440001',
                status: 'completed',
                progress: 100,
                startTime: new Date('2024-01-15T10:00:00Z'),
                endTime: new Date('2024-01-15T14:30:00Z'),
                epochs: 10,
                currentEpoch: 10,
                loss: '0.15',
                accuracy: '0.94',
                batchSize: 32,
                learningRate: '0.001',
            },
            {
                id: '650e8400-e29b-41d4-a716-446655440002',
                modelId: '550e8400-e29b-41d4-a716-446655440003',
                status: 'completed',
                progress: 100,
                startTime: new Date('2024-01-08T08:00:00Z'),
                endTime: new Date('2024-01-08T16:30:00Z'),
                epochs: 20,
                currentEpoch: 20,
                loss: '0.12',
                accuracy: '0.87',
                batchSize: 64,
                learningRate: '0.0005',
            },
        ]).onConflictDoNothing().returning();
        console.log(`✅ Inserted ${jobs.length} training jobs`);
        // Insert sample metrics
        const metrics = await db.insert(modelMetrics).values([
            {
                modelId: '550e8400-e29b-41d4-a716-446655440001',
                accuracy: '0.94',
                precision: '0.92',
                recall: '0.89',
                f1Score: '0.90',
                latency: 45,
                throughput: 1200,
                memoryUsage: '2.1',
                cpuUsage: '35',
            },
            {
                modelId: '550e8400-e29b-41d4-a716-446655440002',
                accuracy: '0.89',
                precision: '0.87',
                recall: '0.85',
                f1Score: '0.86',
                latency: 120,
                throughput: 800,
                memoryUsage: '8.5',
                cpuUsage: '65',
            },
            {
                modelId: '550e8400-e29b-41d4-a716-446655440003',
                accuracy: '0.87',
                precision: '0.85',
                recall: '0.82',
                f1Score: '0.83',
                latency: 25,
                throughput: 2000,
                memoryUsage: '0.5',
                cpuUsage: '15',
            },
            {
                modelId: '550e8400-e29b-41d4-a716-446655440004',
                accuracy: '0.91',
                precision: '0.88',
                recall: '0.86',
                f1Score: '0.87',
                latency: 2500,
                throughput: 10,
                memoryUsage: '12.0',
                cpuUsage: '85',
            },
        ]).onConflictDoNothing().returning();
        console.log(`✅ Inserted ${metrics.length} model metrics`);
        console.log('🎉 Database seeded successfully!');
    }
    catch (error) {
        console.error('❌ Seeding failed:', error);
        process.exit(1);
    }
    process.exit(0);
}
seed();
//# sourceMappingURL=seed.js.map