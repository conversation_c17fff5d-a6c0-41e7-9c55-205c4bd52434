import { config } from 'dotenv';
config();
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schemas';
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
    throw new Error('DATABASE_URL is not set');
}
// Create the connection
const client = postgres(connectionString, {
    max: 20,
    idle_timeout: 20,
    connect_timeout: 10,
});
// Create the database instance
export const db = drizzle(client, { schema });
// Export the client for cleanup
export { client };
// Export all schemas
export * from './schemas';
//# sourceMappingURL=index.js.map