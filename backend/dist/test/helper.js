import fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import multipart from '@fastify/multipart';
import cookie from '@fastify/cookie';
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify';
import { appRouter } from '../routes/index.js';
import { createContext } from '../lib/trpc.js';
export async function build() {
    const app = fastify({
        logger: false, // Disable logging during tests
    });
    // Register plugins
    await app.register(helmet, {
        contentSecurityPolicy: false,
    });
    await app.register(cors, {
        origin: true,
        credentials: true,
    });
    await app.register(rateLimit, {
        max: 1000, // Higher limit for tests
        timeWindow: '1 minute',
        skipOnError: true,
    });
    await app.register(cookie, {
        secret: 'test-secret',
    });
    await app.register(multipart, {
        limits: {
            fileSize: 10 * 1024 * 1024, // 10MB
            files: 5,
        },
    });
    // tRPC plugin
    await app.register(fastifyTRPCPlugin, {
        prefix: '/trpc',
        trpcOptions: {
            router: appRouter,
            createContext,
            onError({ path, error }) {
                console.error('tRPC test error:', { path, error });
            },
        },
    });
    // Root endpoint with API documentation
    app.get('/', async (_request, reply) => {
        const apiInfo = {
            name: 'W-O-W Backend API (Test)',
            version: '0.1.0',
            description: 'Backend API for W-O-W application - Test Environment',
            endpoints: {
                health: '/health',
                metrics: '/metrics',
                trpc: '/trpc'
            },
            status: 'testing',
            timestamp: new Date().toISOString()
        };
        reply.type('application/json').send(apiInfo);
    });
    // Health check endpoint
    app.get('/health', async (_request, reply) => {
        const health = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            services: {
                redis: true, // Mock for tests
                milvus: false,
            },
        };
        reply.status(200).send(health);
    });
    // Metrics endpoint
    app.get('/metrics', async (_request, reply) => {
        const metrics = `
# HELP wow_uptime_seconds Application uptime in seconds
# TYPE wow_uptime_seconds counter
wow_uptime_seconds ${process.uptime()}

# HELP wow_memory_usage_bytes Memory usage in bytes
# TYPE wow_memory_usage_bytes gauge
wow_memory_usage_bytes{type="rss"} ${process.memoryUsage().rss}
wow_memory_usage_bytes{type="heapTotal"} ${process.memoryUsage().heapTotal}
wow_memory_usage_bytes{type="heapUsed"} ${process.memoryUsage().heapUsed}
wow_memory_usage_bytes{type="external"} ${process.memoryUsage().external}
    `.trim();
        reply.type('text/plain').send(metrics);
    });
    return app;
}
//# sourceMappingURL=helper.js.map