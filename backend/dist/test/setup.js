import { config } from 'dotenv';
// Load test environment variables
config({ path: '.env' });
// Override environment for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;
process.env.REDIS_URL = process.env.TEST_REDIS_URL || process.env.REDIS_URL;
// Global test setup
console.log('🧪 Test environment setup complete');
//# sourceMappingURL=setup.js.map