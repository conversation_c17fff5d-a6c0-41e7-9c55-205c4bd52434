import fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import multipart from '@fastify/multipart';
import cookie from '@fastify/cookie';
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify';
import { config } from 'dotenv';
import { appRouter } from './routes/index.js';
import { createContext } from './lib/trpc.js';
import { logger } from './utils/logger.js';
import { redisService } from './services/cache/redis.js';
// Force import and initialization of the real-time monitor WebSocket server
import './services/realtime-monitor.js';
import { milvusService } from './services/vector/milvus.js';
import { initializeMinIO } from './lib/minio.js';
// Load environment variables
config();
const PORT = Number(process.env.PORT) || 4001;
const HOST = process.env.HOST || '0.0.0.0';
const NODE_ENV = process.env.NODE_ENV || 'development';
// Create Fastify instance
const server = fastify({
    logger: true,
    trustProxy: true,
    bodyLimit: 50 * 1024 * 1024, // 50MB limit for file uploads
});
// Global error handler
server.setErrorHandler((error, request, reply) => {
    logger.error({ error, url: request.url, method: request.method }, 'Request error');
    if (error.validation) {
        reply.status(400).send({
            error: 'Validation Error',
            message: error.message,
            details: error.validation,
        });
        return;
    }
    if (error.statusCode) {
        reply.status(error.statusCode).send({
            error: error.name,
            message: error.message,
        });
        return;
    }
    reply.status(500).send({
        error: 'Internal Server Error',
        message: NODE_ENV === 'development' ? error.message : 'Something went wrong',
    });
});
// Register plugins
async function setupPlugins() {
    // Security plugins
    await server.register(helmet, {
        contentSecurityPolicy: false, // Disable CSP for API
    });
    await server.register(cors, {
        origin: NODE_ENV === 'development'
            ? ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:4002'] // Add your frontend URLs
            : process.env.ALLOWED_ORIGINS?.split(',') || false,
        credentials: true,
    });
    await server.register(rateLimit, {
        max: 100,
        timeWindow: '1 minute',
        skipOnError: true,
    });
    // Cookie support
    await server.register(cookie, {
        secret: process.env.SESSION_SECRET || 'fallback-secret-for-development',
    });
    // File upload support
    await server.register(multipart, {
        limits: {
            fileSize: 10 * 1024 * 1024, // 10MB
            files: 5,
        },
    });
    // tRPC plugin
    await server.register(fastifyTRPCPlugin, {
        prefix: '/trpc',
        trpcOptions: {
            router: appRouter,
            createContext,
            onError({ path, error }) {
                logger.error({ path, error }, 'tRPC error');
            },
        },
    });
}
// Root endpoint with API documentation
server.get('/', async (_request, reply) => {
    const apiInfo = {
        name: 'W-O-W Backend API',
        version: '0.1.0',
        description: 'Backend API for W-O-W application',
        endpoints: {
            health: '/health',
            metrics: '/metrics',
            trpc: '/trpc',
            api_routes: {
                auth: {
                    session: 'GET /trpc/auth.session',
                    register: 'POST /trpc/auth.register',
                    login: 'POST /trpc/auth.login',
                    logout: 'POST /trpc/auth.logout'
                },
                users: {
                    me: 'GET /trpc/users.me',
                    getById: 'GET /trpc/users.getById',
                    updateProfile: 'POST /trpc/users.updateProfile',
                    list: 'GET /trpc/users.list (admin only)'
                }
            }
        },
        documentation: 'https://trpc.io/docs',
        status: 'running',
        timestamp: new Date().toISOString()
    };
    reply.type('application/json').send(apiInfo);
});
// Health check endpoint
server.get('/health', async (_request, reply) => {
    const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        services: {
            redis: redisService.isConnected(),
            milvus: false,
        },
    };
    // Check service health
    try {
        await redisService.ping();
        health.services.redis = true;
    }
    catch {
        health.services.redis = false;
    }
    try {
        await milvusService.checkHealth();
        health.services.milvus = true;
    }
    catch {
        health.services.milvus = false;
    }
    const allHealthy = Object.values(health.services).every(Boolean);
    reply.status(allHealthy ? 200 : 503).send(health);
});
// Metrics endpoint for Prometheus
server.get('/metrics', async (_request, reply) => {
    // Basic metrics - you can expand this with prom-client
    const metrics = `
# HELP wow_uptime_seconds Application uptime in seconds
# TYPE wow_uptime_seconds counter
wow_uptime_seconds ${process.uptime()}

# HELP wow_memory_usage_bytes Memory usage in bytes
# TYPE wow_memory_usage_bytes gauge
wow_memory_usage_bytes{type="rss"} ${process.memoryUsage().rss}
wow_memory_usage_bytes{type="heapTotal"} ${process.memoryUsage().heapTotal}
wow_memory_usage_bytes{type="heapUsed"} ${process.memoryUsage().heapUsed}
wow_memory_usage_bytes{type="external"} ${process.memoryUsage().external}
  `.trim();
    reply.type('text/plain').send(metrics);
});
// Initialize services and start server
async function start() {
    try {
        console.log('Starting server setup...');
        // Setup plugins
        console.log('Setting up plugins...');
        await setupPlugins();
        console.log('✅ Plugins setup complete');
        // Connect to external services
        console.log('Connecting to external services...');
        console.log('Connecting to Redis...');
        await redisService.connect();
        console.log('✅ Redis connected');
        console.log('Connecting to Milvus...');
        await milvusService.connect();
        console.log('✅ Milvus connected');
        console.log('Initializing MinIO...');
        await initializeMinIO();
        console.log('✅ MinIO initialized');
        // WebSocket server for real-time monitoring is already started automatically
        console.log('✅ WebSocket server is running on port 8080 for real-time monitoring');
        // Start server
        console.log('Starting Fastify server...');
        await server.listen({ port: PORT, host: HOST });
        console.log(`🚀 Server running on http://${HOST}:${PORT}`);
        console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
        console.log(`🔧 tRPC endpoint: http://${HOST}:${PORT}/trpc`);
    }
    catch (error) {
        console.error('Failed to start server:', error);
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Graceful shutdown
async function gracefulShutdown() {
    logger.info('Received shutdown signal, closing server...');
    try {
        await server.close();
        await redisService.disconnect();
        await milvusService.disconnect();
        logger.info('✅ Server closed gracefully');
        process.exit(0);
    }
    catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
    }
}
// Handle shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
// Handle uncaught errors
process.on('uncaughtException', (error) => {
    logger.fatal({ error }, 'Uncaught exception');
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger.fatal({ reason, promise }, 'Unhandled rejection');
    process.exit(1);
});
// Start the server
start();
//# sourceMappingURL=server.js.map