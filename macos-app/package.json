{"name": "wow-macos-app", "version": "1.0.0", "description": "Native macOS app for W-O-W project", "main": "index.js", "scripts": {"tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:build:debug": "tauri build --debug", "dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\" \"npm run tauri:dev\"", "backend:dev": "cd ../backend && npm run dev", "frontend:dev": "cd ../backend-gui && npm run dev", "build": "npm run backend:build && npm run frontend:build && npm run tauri:build", "backend:build": "cd ../backend && npm run build", "frontend:build": "cd ../backend-gui && npm run build"}, "keywords": ["tauri", "macos", "native", "app"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"@tauri-apps/cli": "^1.5.0", "concurrently": "^8.2.2"}}