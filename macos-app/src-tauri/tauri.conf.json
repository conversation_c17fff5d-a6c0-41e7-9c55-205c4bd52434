{"build": {"beforeDevCommand": "npm run frontend:dev", "beforeBuildCommand": "npm run frontend:build", "devPath": "http://localhost:5173", "distDir": "../backend-gui/dist", "withGlobalTauri": false}, "package": {"productName": "W-O-W Dashboard", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true, "exists": true}, "path": {"all": true}, "http": {"all": true, "request": true}, "notification": {"all": true}, "process": {"all": false, "relaunch": true, "exit": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.wow.dashboard", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "category": "DeveloperTool", "shortDescription": "W-O-W Dashboard - Backend Management Tool", "longDescription": "A comprehensive dashboard for managing backend services, databases, and configurations for the W-O-W project.", "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "resources": [], "externalBin": [], "copyright": "", "licenseKey": ""}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "W-O-W Dashboard", "width": 1400, "height": 900, "minWidth": 1200, "minHeight": 700, "center": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "titleBarStyle": "Visible"}], "systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}}