[package]
name = "wow-macos-app"
version = "1.0.0"
description = "Native macOS app for W-O-W project"
authors = ["<PERSON>"]
license = "MIT"
repository = ""
default-run = "wow-macos-app"
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5", features = ["api-all", "system-tray", "updater"] }
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
default = [ "custom-protocol" ]
custom-protocol = [ "tauri/custom-protocol" ]