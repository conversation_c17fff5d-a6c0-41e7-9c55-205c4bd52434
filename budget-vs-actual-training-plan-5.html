<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Budget vs Actual Training Plan - Executive Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .executive-gradient { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
        .corporate-card { background: #ffffff; border: 1px solid #e2e8f0; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); }
        .metric-highlight { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); }
        .status-indicator { width: 8px; height: 8px; border-radius: 50%; }
        .executive-table { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .chart-container { background: #fafbfc; border: 1px solid #e5e7eb; }
    </style>
</head>
<body class="executive-gradient min-h-screen">
    <!-- Executive Header -->
    <header class="bg-white border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Training Budget Executive Dashboard</h1>
                    <p class="text-gray-600 text-sm mt-1">Fiscal Year 2024 • Budget vs Actual Performance Analysis</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Last Updated</p>
                        <p class="text-sm font-medium text-gray-900">October 15, 2024</p>
                    </div>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>Executive Summary
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- Executive Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="corporate-card rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Total Budget</h3>
                    <i class="fas fa-dollar-sign text-blue-600"></i>
                </div>
                <p class="text-3xl font-bold text-gray-900">$125,000</p>
                <p class="text-sm text-gray-600 mt-2">Allocated for FY2024</p>
            </div>

            <div class="corporate-card rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Actual Expenditure</h3>
                    <i class="fas fa-chart-bar text-green-600"></i>
                </div>
                <p class="text-3xl font-bold text-gray-900">$98,750</p>
                <p class="text-sm text-green-600 mt-2">79% of budget utilized</p>
            </div>

            <div class="corporate-card rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Budget Variance</h3>
                    <i class="fas fa-balance-scale text-purple-600"></i>
                </div>
                <p class="text-3xl font-bold text-green-600">+$26,250</p>
                <p class="text-sm text-gray-600 mt-2">21% under budget</p>
            </div>

            <div class="corporate-card rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Training ROI</h3>
                    <i class="fas fa-trending-up text-orange-600"></i>
                </div>
                <p class="text-3xl font-bold text-gray-900">285%</p>
                <p class="text-sm text-orange-600 mt-2">Excellent performance</p>
            </div>
        </div>

        <!-- Key Insights Section -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Budget Performance -->
            <div class="lg:col-span-2 corporate-card rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Budget Performance Trend</h3>
                <div class="chart-container rounded-lg p-4">
                    <canvas id="performanceChart" width="600" height="300"></canvas>
                </div>
            </div>

            <!-- Program Status -->
            <div class="corporate-card rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Program Status</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="status-indicator bg-green-500 mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">On Track</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">2 Programs</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="status-indicator bg-red-500 mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">Over Budget</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">1 Program</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="status-indicator bg-yellow-500 mr-3"></div>
                            <span class="text-sm font-medium text-gray-900">Planned</span>
                        </div>
                        <span class="text-sm font-bold text-gray-900">1 Program</span>
                    </div>
                </div>
                
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Budget Allocation</h4>
                    <canvas id="allocationChart" width="250" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- Program Details Table -->
        <div class="corporate-card rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Training Program Details</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full executive-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Participants</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Allocated</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual Spent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Leadership Development Program</div>
                                <div class="text-sm text-gray-500">Executive & Senior Management</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Human Resources</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$35,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$28,500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">+$6,500</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">75%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">On Track</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Technical Skills Certification</div>
                                <div class="text-sm text-gray-500">IT & Software Development</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Information Technology</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">120</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$45,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$42,750</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">+$2,250</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">90%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">On Track</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Compliance & Safety Training</div>
                                <div class="text-sm text-gray-500">Regulatory & Safety Standards</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Legal & Compliance</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">82</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$25,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$27,500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">-$2,500</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-red-600 h-2 rounded-full" style="width: 100%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">100%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Over Budget</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Soft Skills Development</div>
                                <div class="text-sm text-gray-500">Communication & Teamwork</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Human Resources</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$20,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">—</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">0%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Scheduled Q4</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Key Insights -->
            <div class="corporate-card rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Key Insights</h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                        <p class="text-sm text-gray-700">Training budget is performing 21% under allocated amount, indicating efficient resource utilization.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                        <p class="text-sm text-gray-700">Technical Skills and Leadership programs are delivering strong ROI with high completion rates.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2 mr-3"></div>
                        <p class="text-sm text-gray-700">Compliance training exceeded budget by 10% due to additional regulatory requirements.</p>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3"></div>
                        <p class="text-sm text-gray-700">Q4 Soft Skills program launch will utilize remaining budget allocation effectively.</p>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="corporate-card rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Strategic Recommendations</h3>
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                        <h4 class="text-sm font-medium text-blue-900">Budget Reallocation</h4>
                        <p class="text-sm text-blue-700 mt-1">Consider reallocating surplus funds to expand high-performing technical training programs.</p>
                    </div>
                    <div class="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <h4 class="text-sm font-medium text-green-900">Program Expansion</h4>
                        <p class="text-sm text-green-700 mt-1">Leadership development shows excellent cost efficiency - recommend increasing capacity for next fiscal year.</p>
                    </div>
                    <div class="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                        <h4 class="text-sm font-medium text-yellow-900">Cost Control</h4>
                        <p class="text-sm text-yellow-700 mt-1">Implement stricter budget controls for compliance training to prevent future overruns.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Performance Chart
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4 (Projected)'],
                datasets: [{
                    label: 'Budget Allocation',
                    data: [30000, 65000, 100000, 125000],
                    borderColor: '#6b7280',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [8, 4],
                    pointBackgroundColor: '#6b7280',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }, {
                    label: 'Actual Spending',
                    data: [28000, 60000, 88000, 98750],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.3,
                    pointBackgroundColor: '#2563eb',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });

        // Allocation Chart
        const allocationCtx = document.getElementById('allocationChart').getContext('2d');
        new Chart(allocationCtx, {
            type: 'doughnut',
            data: {
                labels: ['Leadership Dev.', 'Technical Skills', 'Compliance', 'Soft Skills'],
                datasets: [{
                    data: [35000, 45000, 25000, 20000],
                    backgroundColor: ['#2563eb', '#059669', '#dc2626', '#d97706'],
                    borderWidth: 0,
                    hoverBorderWidth: 2,
                    hoverBorderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                cutout: '65%'
            }
        });
    </script>
</body>
</html>